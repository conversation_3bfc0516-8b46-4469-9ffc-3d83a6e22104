# Project Handover Document
## Employee Management System (EMS) – Mobile Application
**Techo Lab (A Unit of MVM Innovations Private Limited)**

**Handover Date:** [Date to be filled]  
**Project Title:** Employee Management System (EMS) – Mobile Application  
**App Name:** AARY

## Purpose
A mobile application for field employees to track waypoints, manage GPS coordinates, capture pole details, and upload data for infrastructure projects. The app enables real-time location tracking, route management, and comprehensive data collection for utility infrastructure projects.

**Backend URL:** https://ems-backend-369113394426.asia-south2.run.app

## Technology Stack

| Layer | Technology |
|-------|------------|
| Mobile Framework | Flutter (Dart 3.7.2+) |
| State Management | GetX (4.7.2) |
| HTTP Client | Dio (5.8.0+1) |
| Maps Integration | Google Maps Flutter (2.12.1) |
| Location Services | Location (8.0.0) |
| Authentication | JWT Decoder (2.0.1) |
| Local Storage | Shared Preferences (2.5.3) |
| Image Handling | Image Picker (1.0.7) |
| UI Components | Custom widgets with Material Design |

## Folder Structure and Architecture
The project follows a modular architecture with clear separation of concerns:

```
lib/
├── common/
│   ├── constants/
│   │   ├── app_colors.dart          # App color scheme and theming
│   │   └── general.dart             # Base URL and API keys
│   ├── utils/
│   │   ├── api_services.dart        # API service functions
│   │   ├── network_client.dart      # Dio error handling and network utilities
│   │   └── shared_preferences.dart  # Token storage and local data management
│   └── widgets/
│       └── custom_app_bar.dart      # Reusable app bar component
├── models/
│   ├── profile_model.dart           # User profile data model
│   └── project_model.dart           # Project data model
├── routes/
│   └── router.dart                  # App routing configuration
├── screens/
│   ├── Bottom_Nav_Bar/
│   │   ├── nav_screen.dart          # Main navigation container
│   │   └── nav_controller.dart      # Navigation state management
│   ├── login/
│   │   ├── login_screen.dart        # Employee authentication screen
│   │   └── login_controller.dart    # Login logic and validation
│   ├── home/
│   │   ├── home_screen.dart         # Project selection and overview
│   │   └── home_controller.dart     # Project management logic
│   ├── map_screen/
│   │   ├── map_screen.dart          # Google Maps integration
│   │   └── map_controller.dart      # Map functionality and waypoint rendering
│   ├── profile/
│   │   ├── profile_screen.dart      # User profile display
│   │   └── profile_controller.dart  # Profile data management
│   ├── gps_details_screen/         # GPS coordinate collection
│   ├── pole_details_screen/        # Infrastructure pole data entry
│   ├── upload_image_screen/        # Image capture and upload
│   ├── way_point_screen/           # Waypoint route configuration
│   ├── create_track_screen/        # Track creation and management
│   ├── history_screen/             # Historical route data
│   ├── select_categories.dart      # Project category selection
│   ├── gas_screen.dart            # Gas infrastructure tracking
│   └── fibre_optics_screen.dart   # Fiber optic infrastructure tracking
└── main.dart                       # Application entry point
```

## Functional Features

### 1. Authentication System
- **JWT-based secure authentication** for field employees
- **Employee ID and password** login system
- **Token persistence** using SharedPreferences with automatic expiry handling
- **Automatic session management** with token validation on app startup
- **Protected routes** ensuring only authenticated users can access app features

### 2. Project Management
- **Project selection** from assigned projects list
- **Real-time project data** fetched from `/api/projects/my-projects`
- **Project-specific waypoint tracking** and data collection
- **Pull-to-refresh** functionality for updated project information

### 3. Waypoint and GPS Tracking
- **Real-time location tracking** using device GPS
- **Google Maps integration** with custom markers and polylines
- **Route visualization** with different colors for new (#FB8500) and existing (black) routes
- **Waypoint data collection** including GPS coordinates, timestamps, and route details
- **Start/End point management** for route tracking
- **Custom marker shapes** (circles, triangles, rectangles) based on infrastructure type

### 4. Data Collection Forms
- **GPS Details Screen:** Date/time, user ID, coordinates, route information, conductor details
- **Pole Details Screen:** Infrastructure specifications, pole types, materials
- **Image Upload:** Camera integration for capturing infrastructure photos
- **Conditional field display** based on conductor selection (Not Applicable vs other values)
- **Form validation** with required field checking

### 5. Map Functionality
- **Interactive Google Maps** with zoom and pan controls
- **Custom markers** for different infrastructure types:
  - Circle markers for standard poles
  - Triangle markers for double pole structures  
  - Rectangle markers for transformers
- **Color-coded markers** (orange for new, black for existing)
- **Polyline rendering** between waypoints
- **Current location tracking** with permission handling

### 6. Data Upload and Synchronization
- **Multipart form data upload** to `/api/projects/{projectId}/waypoints`
- **Image attachment** with captured photos
- **GPS and pole details** conversion to JSON strings
- **Comprehensive error handling** with user feedback
- **Upload progress tracking** and success notifications

### 7. History and Route Management
- **Historical route viewing** from `/api/projects/employee-waypoints`
- **Route completion tracking** with isStart/isEnd flags
- **Waypoint status management** for continuing or completing routes
- **Route visualization** on maps with historical data

## Development Setup

### Prerequisites
- Flutter SDK (3.27.0+)
- Dart SDK (3.7.2+)
- Android Studio / VS Code
- Git

### Setup Instructions
```bash
git clone <repository-url>
cd EMS_App
flutter pub get
```

### Environment Configuration
The app uses hardcoded configuration in `lib/common/constants/general.dart`:
```dart
String baseUrl = 'https://ems-backend-369113394426.asia-south2.run.app';
String googleApiKey = 'AIzaSyCq7K2btBU_YbcGc2RjhS3GXQiqyUEbXdM';
```

### Run the Application
```bash
flutter run
```

## Authentication and Authorization

### Authentication Flow
**Files:** `login_screen.dart`, `login_controller.dart`, `shared_preferences.dart`

1. Employee enters Employee ID and password
2. POST request to `/api/auth/login-employee`
3. JWT token stored in SharedPreferences
4. Token validation on app startup using `jwt_decoder`
5. Automatic navigation based on authentication status

### Token Management
- **Automatic token validation** on app startup
- **Token expiry handling** with automatic logout
- **Secure token storage** using SharedPreferences
- **Authorization headers** automatically added to all API requests

## API Integration

### Dio Configuration
**File:** `network_client.dart`
- **Automatic error handling** with user-friendly messages
- **Request/response logging** for debugging
- **Timeout configuration** for network requests
- **Custom exception handling** for different error types

### API Endpoints Used

| Feature | Method | Endpoint |
|---------|--------|----------|
| Employee Login | POST | `/api/auth/login-employee` |
| Get Profile | GET | `/api/auth/me` |
| Get Projects | GET | `/api/projects/my-projects` |
| Get Waypoints | GET | `/api/projects/{projectId}/waypoints` |
| Upload Waypoint | POST | `/api/projects/{projectId}/waypoints` |
| Employee Waypoints | GET | `/api/projects/employee-waypoints` |

## State Management with GetX

### Controllers Architecture
- **Reactive state management** using GetX observables
- **Dependency injection** with Get.put() and Get.find()
- **Lifecycle management** with automatic controller disposal
- **Cross-controller communication** for data sharing

### Key Controllers
- **NavController:** Bottom navigation and screen management
- **HomeController:** Project selection and management
- **MapController:** Google Maps and location services
- **LoginController:** Authentication logic
- **UploadDataController:** Form data collection and management

## UI/UX Design

### Design System
- **Custom color scheme** defined in `app_colors.dart`
- **Consistent typography** and spacing
- **Material Design principles** with custom components
- **Responsive design** for different screen sizes
- **Dark theme support** for better visibility in field conditions

### User Experience Features
- **Loading states** with progress indicators
- **Error handling** with informative snackbars
- **Form validation** with real-time feedback
- **Pull-to-refresh** for data updates
- **Offline capability** considerations for field use

## Error Handling and User Experience

### Network Error Handling
- **Connection timeout** management
- **No internet** detection and user notification
- **API error** parsing with meaningful messages
- **Retry mechanisms** for failed requests

### User Feedback
- **Success notifications** for completed actions
- **Error messages** with actionable information
- **Loading indicators** during async operations
- **Form validation** with inline error messages

## Final Notes

### Code Quality
- **Clean architecture** with separation of concerns
- **Modular design** for easy maintenance and testing
- **Comprehensive error handling** throughout the application
- **Consistent coding standards** following Dart/Flutter best practices

### Scalability
- **Modular controller architecture** allows easy feature additions
- **API abstraction** makes backend changes manageable
- **Reusable components** reduce code duplication
- **State management** supports complex data flows

### Field-Ready Features
- **Offline considerations** for areas with poor connectivity
- **Battery optimization** with efficient location tracking
- **User-friendly interface** designed for field workers
- **Comprehensive data collection** for infrastructure management

The application provides a complete solution for field data collection with robust error handling, intuitive user interface, and comprehensive project management capabilities.
