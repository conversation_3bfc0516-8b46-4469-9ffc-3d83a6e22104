import 'package:flutter/material.dart';

Widget roundButton({required IconData icon, VoidCallback? onPressed}) {
    return Material(
      color: const Color(0xFF4A3C2F),
      shape: const CircleBorder(),
      clipBehavior: Clip.antiAlias,
      elevation: 2,
      child: InkWell(
        onTap: onPressed ?? () {},
        child: SizedB<PERSON>(
          width: 48,
          height: 48,
          child: Icon(icon, color: Colors.white, size: 24),
        ),
      ),
    );
  }