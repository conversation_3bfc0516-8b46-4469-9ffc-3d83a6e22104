import 'package:ems/common/constants/app_colors.dart';
import 'package:flutter/material.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;
  final Color? color;
  final Color? textColor;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final double? width;
  final BoxDecoration? decoration;

  const CustomButton({
    super.key,
    required this.onTap,
    required this.text,
    this.color,
    this.textColor,
    this.padding,
    this.borderRadius,
    this.width,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    final defaultDecoration = BoxDecoration(
      borderRadius: borderRadius ?? BorderRadius.circular(30),
      color: color ?? AppColors.primaryOrange,
    );

    return InkWell(
      onTap: onTap,
      borderRadius: borderRadius ?? BorderRadius.circular(30),
      child: Container(
        width: width ?? double.infinity,
        padding: padding ?? const EdgeInsets.symmetric(vertical: 15),
        decoration: decoration ?? defaultDecoration,
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              color: textColor ?? Colors.white,
              fontSize: 15,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }
}