import 'package:ems/common/widgets/custom_circularprogressindicator.dart';
import 'package:flutter/material.dart';

class CustomInputField extends StatefulWidget {
  final List<String> items;
  final String? value;
  final Function(String?) onChanged;
  final bool isEditable;
  final bool isLoading;
  final bool showDropdown;

  const CustomInputField({
    super.key,
    required this.items,
    required this.value,
    required this.onChanged,
    this.isEditable = false,
    this.showDropdown = false,
    this.isLoading = false,
  });

  @override
  State<CustomInputField> createState() => _CustomInputFieldState();
}

class _CustomInputFieldState extends State<CustomInputField> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value ?? '');
  }

  @override
  void didUpdateWidget(covariant CustomInputField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != _controller.text) {
      _controller.text = widget.value ?? '';
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleDropdownSelection(String? selected) {
    if (selected != null) {
      _controller.text = selected;
      widget.onChanged(selected);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        height: 40,
        padding: const EdgeInsets.fromLTRB(10, 1, 0, 1),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.black),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: _controller,
                readOnly: !widget.isEditable,
                onChanged: widget.isEditable ? widget.onChanged : null,
                style: TextStyle(color: Colors.black, fontSize: 12),
                decoration:
                    widget.isLoading
                        ? InputDecoration(
                          border: InputBorder.none,
                          isDense: true,
                          contentPadding: const EdgeInsets.symmetric(
                            vertical: 10,
                          ),
                          suffixIcon: const SizedBox(
                            height: 20,
                            width: 20,
                            child: Center(
                              child: CustomCircularProgressIndicator(size: 14),
                            ),
                          ),
                        )
                        : const InputDecoration.collapsed(hintText: ''),
              ),
            ),
            if (widget.showDropdown)
              PopupMenuButton<String>(
                icon: const Icon(Icons.arrow_drop_down, color: Colors.black),
                onSelected: _handleDropdownSelection,
                itemBuilder:
                    (context) =>
                        widget.items
                            .map(
                              (item) => PopupMenuItem(
                                value: item,
                                child: Text(
                                  item,
                                  style: const TextStyle(fontSize: 12),
                                ),
                              ),
                            )
                            .toList(),
              ),
          ],
        ),
      ),
    );
  }
}
