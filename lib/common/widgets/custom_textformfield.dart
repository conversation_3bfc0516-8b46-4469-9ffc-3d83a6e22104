import 'package:ems/common/constants/app_colors.dart';
import 'package:flutter/material.dart';

class CustomTextformfield extends StatelessWidget {
  const CustomTextformfield({
    super.key,
    this.label,
    this.controller,
    this.validator,
    this.keyboardType = TextInputType.text,
    this.isPassword = false,
    this.prefixIcon,
    this.suffixIcon,
    this.hintText,
    this.icon,
    this.suffixTap,
    this.readOnly = false,
    this.fillColor = Colors.transparent,
  });

  final String? label;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final TextInputType keyboardType;
  final bool isPassword;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? hintText;
  final IconData? icon;
  final GestureTapCallback? suffixTap;
  final bool readOnly;
  final Color fillColor;

  @override
  Widget build(BuildContext context) {
    Widget textField = TextFormField(
      controller: controller,
      validator: validator,
      keyboardType: keyboardType,
      textInputAction: TextInputAction.next,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      readOnly: readOnly,
      obscureText: isPassword,
      style: const TextStyle(
        fontSize: 15,
        fontWeight: FontWeight.w400,
        color: AppColors.black,
      ),
      decoration: InputDecoration(
        filled: true,
        fillColor: fillColor,
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: AppColors.black, width: 1.0),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: AppColors.primaryOrange, width: 1.0),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: AppColors.red, width: 1.0),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: AppColors.red, width: 2.0),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: Colors.grey.shade400, width: 1.0),
        ),
        prefixIcon: prefixIcon,
        suffixIcon:
            suffixTap != null
                ? InkWell(
                  onTap: suffixTap,
                  child: suffixIcon ?? (icon != null ? Icon(icon) : null),
                )
                : suffixIcon ?? (icon != null ? Icon(icon) : null),
        hintText: hintText,
        hintStyle: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.normal,
          color: AppColors.black,
        ),
      ),
    );
    return suffixTap != null
        ? textField
        : textField;
  }
}
