import 'package:ems/common/constants/size.dart';
import 'package:ems/common/widgets/custom_input_field.dart';
import 'package:flutter/material.dart';

class CustomDropdownRow extends StatelessWidget {
  final String title;
  final List<String> items;
  final String? value;
  final Function(String?) onChanged;

  final bool isEditable;
  final bool showDropdown;
  final bool isLoading;

  const CustomDropdownRow({
    super.key,
    required this.title,
    required this.items,
    required this.onChanged,
    required this.value,
    this.isEditable = false,
    this.showDropdown = false,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            flex: 1,
            child: Text(
              title,
              style: const TextStyle(color: Colors.white, fontSize: 14, decoration: TextDecoration.none),
            ),
          ),
          0.625.pw,
          const Text(":", style: TextStyle(color: Colors.white, fontSize: 14, decoration: TextDecoration.none)),
          1.25.pw,
          Expanded(
            flex: 1,
            child: CustomInputField(
              items: items,
              value: value,
              onChanged: onChanged,
              isEditable: isEditable,
              showDropdown: showDropdown,
              isLoading: isLoading,
            ),
          ),
        ],
      ),
    );
  }
}
