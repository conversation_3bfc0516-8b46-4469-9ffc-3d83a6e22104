import 'package:ems/common/constants/size.dart';
import 'package:flutter/material.dart';

class CustomCard extends StatelessWidget {
  final String routeName;
  final VoidCallback? onTap;
  final Color? iconColor;
  final Color? cardColor;
  final IconData icon;
  final double borderRadius;

  const CustomCard({
    super.key,
    required this.routeName,
    this.onTap,
    this.iconColor,
    this.cardColor,
    required this.icon,
    this.borderRadius = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        color: cardColor,
        margin: const EdgeInsets.only(bottom: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
          child: Container(
            constraints: const BoxConstraints(minHeight: 80),
            child: Row(
              children: [
                Icon(
                  icon,
                  size: 30,
                  color: iconColor ?? Theme.of(context).primaryColor,
                ),
                2.pw,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        routeName,
                        style: const TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      1.ph,
                      Text(
                        'See waypoints created by you',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}