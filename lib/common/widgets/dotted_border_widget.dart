import 'package:dotted_border/dotted_border.dart';
import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/common/constants/size.dart';
import 'package:ems/common/widgets/custom_button.dart';
import 'package:flutter/material.dart';

class DottedBorderWidget extends StatelessWidget {
  final VoidCallback onTap;
  final VoidCallback? onCameraTap;
  final bool isDisabled;

  const DottedBorderWidget({
    super.key,
    required this.onTap,
    this.onCameraTap,
    this.isDisabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isDisabled ? null : onTap,
      child: DottedBorder(
        borderType: BorderType.RRect,
        radius: Radius.circular(12),
        dashPattern: [8, 4],
        color: isDisabled ? Colors.grey.shade300 : Colors.grey,
        strokeWidth: 1,
        child: Container(
          width: double.infinity,
          height: 300,
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isDisabled ? Icons.check_circle : Icons.cloud_upload,
                size: 40,
                color: isDisabled ? Colors.green : AppColors.primaryOrange,
              ),
              5.ph,
              Text(
                isDisabled
                    ? 'Image selected (maximum 1 image)'
                    : 'Drag and Drop Your Images Here',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: isDisabled ? Colors.green.shade700 : Colors.black54,
                ),
              ),
              5.ph,
              isDisabled
                  ? Container(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey.shade400,
                    ),
                    width: double.infinity,
                    child: const Center(
                      child: Text(
                        'Maximum Reached',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  )
                  : Column(
                      children: [
                        CustomButton(
                          color: AppColors.primaryOrange,
                          onTap: onTap,
                          text: 'Browse Files',
                          textColor: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        1.ph,
                        CustomButton(
                          color: AppColors.primaryOrange,
                          onTap: onCameraTap ?? () {},
                          text: 'Take Photo',
                          textColor: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ],
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
