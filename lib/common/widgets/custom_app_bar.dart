import 'package:ems/common/constants/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final bool showBackButton;
  final Color backgroundColor;
  final Color textColor;
  final Color backButtonColor;
  final VoidCallback? onPressed;
  final double elevation;
  final bool scrollable;

  const CustomAppBar({
    super.key,
    this.title = "",
    this.showBackButton = true,
    this.backgroundColor = AppColors.white,
    this.textColor = AppColors.black,
    this.backButtonColor = AppColors.black,
    this.onPressed,
    this.elevation = 0,
    this.scrollable = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: textColor,
        ),
      ),
      centerTitle: true,
      leading:
          showBackButton
              ? IconButton(
                onPressed: onPressed ?? () => Get.back(),
                icon: Icon(Icons.arrow_back, color: backButtonColor),
              )
              : null,
      backgroundColor: backgroundColor,
      elevation: elevation,
      automaticallyImplyLeading: false,
      iconTheme: IconThemeData(color: backButtonColor),
      scrolledUnderElevation:
          scrollable ? 4.0 : 0,
      surfaceTintColor: Colors.transparent,
      shadowColor: Colors.black.withAlpha(26),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);
}
