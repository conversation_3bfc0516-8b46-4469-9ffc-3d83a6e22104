import 'package:flutter/material.dart';

class CustomHeader extends StatelessWidget {
  final String title;
  final bool isEnabled;
  final Color? textColor;

  const CustomHeader({
    super.key,
    required this.title,
    this.isEnabled = true,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    final Color effectiveTextColor = textColor ??
        (isEnabled ? Colors.white : Colors.white60);

    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isEnabled
              ? Colors.orange
              : const Color.fromARGB(128, 255, 165, 0),
          borderRadius: BorderRadius.circular(30),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              decoration: TextDecoration.none,
              color: effectiveTextColor,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }
}