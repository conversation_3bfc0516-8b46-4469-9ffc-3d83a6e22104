import 'dart:math' show cos, sqrt, asin, sin, pi;
import 'package:dio/dio.dart';
import 'package:ems/common/constants/general.dart';

class DistanceCalculator {
  static final Dio _dioClient = Dio();

  static Future<double> calculateDistance(
    double startLat,
    double startLng,
    double endLat,
    double endLng,
  ) async {
    try {
      final String url =
          'https://maps.googleapis.com/maps/api/distancematrix/json'
          '?origins=$startLat,$startLng'
          '&destinations=$endLat,$endLng'
          '&mode=walking'
          '&key=$googleApiKey';

      final response = await _dioClient.get(url);

      if (response.statusCode == 200 &&
          response.data['status'] == 'OK' &&
          response.data['rows'][0]['elements'][0]['status'] == 'OK') {
        final int distanceInMeters =
            response.data['rows'][0]['elements'][0]['distance']['value'];

        return distanceInMeters.toDouble();
      } else {
        return calculateHaversineDistance(startLat, startLng, endLat, endLng) *
            1000;
      }
    } catch (e) {
      return calculateHaversineDistance(startLat, startLng, endLat, endLng) *
          1000;
    }
  }

  static double calculateHaversineDistance(
    double startLat,
    double startLng,
    double endLat,
    double endLng,
  ) {
    const int earthRadius = 6371;

    final double latDiff = _toRadians(endLat - startLat);
    final double lngDiff = _toRadians(endLng - startLng);

    final double a =
        sin(latDiff / 2) * sin(latDiff / 2) +
        cos(_toRadians(startLat)) *
            cos(_toRadians(endLat)) *
            sin(lngDiff / 2) *
            sin(lngDiff / 2);
    final double c = 2 * asin(sqrt(a));

    final double distance = earthRadius * c;

    return distance;
  }

  static double _toRadians(double degree) {
    return degree * (pi / 180);
  }
}
