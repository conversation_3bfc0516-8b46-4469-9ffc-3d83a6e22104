import 'package:dio/dio.dart';
import 'package:ems/common/constants/general.dart';
import 'package:ems/common/utils/network_client.dart';
import 'package:ems/models/profile_model.dart';
import 'package:ems/models/project_model.dart';

var dioClient = Dio();

Future<ProfileModel> getUserProfile(String? authToken) async {
  try {
    var response = await dioClient.get(
      "$baseUrl/api/auth/me",
      options: Options(headers: {'Authorization': 'Bearer $authToken'}),
    );

    if (response.statusCode == 200) {
      ProfileModel profile = ProfileModel.fromJson(
        response.data['user'] as Map<String, dynamic>,
      );

      return profile;
    }
  } on DioException catch (e) {
    DioExceptions dioExceptions = DioExceptions.fromDioError(
      dioError: e,
      errorFrom: "Show Profile",
    );
    DioExceptions.showErrorMessage(message: dioExceptions.errorMessage());
  }

  return ProfileModel();
}

Future<ProjectModel> getUserProjects(String? authToken) async {
  try {
    var response = await dioClient.get(
      "$baseUrl/api/projects/my-projects",
      options: Options(headers: {'Authorization': 'Bearer $authToken'}),
    );

    if (response.statusCode == 200) {
      ProjectModel projects = ProjectModel.fromJson(
        response.data as Map<String, dynamic>,
      );

      return projects;
    }
  } on DioException catch (e) {

    if (e.response?.statusCode != 404) {
      DioExceptions dioExceptions = DioExceptions.fromDioError(
        dioError: e,
        errorFrom: "Get Projects",
      );
      DioExceptions.showErrorMessage(message: dioExceptions.errorMessage());
    }

    rethrow;
  }

  return ProjectModel();
}
