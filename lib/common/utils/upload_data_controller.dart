import 'package:get/get.dart';

class UploadDataController extends GetxController{

  // Waypoint Screen
  final RxString waypointName = ''.obs;
  final RxString waypointDescription = ''.obs;
  final RxString waypointDistance = ''.obs;
  final RxString waypointLatitude = ''.obs;
  final RxString waypointLongitude = ''.obs;
  final RxString routeType = ''.obs;
  final RxString routeStartingPoint = ''.obs;
  final RxString routeEndingPoint = ''.obs;

  // Gps Details Screen
  final RxString dateTime = ''.obs;
  final RxString userId = ''.obs;
  final RxString district = ''.obs;
  final RxString routeStartPoint = ''.obs;
  final RxString length = ''.obs;
  final RxString gpsCoordinatesStartPoint = ''.obs;
  final RxString coordinatesCurrentWaypoint = ''.obs;
  final RxString substationName = ''.obs;
  final RxString feederName = ''.obs;
  final RxString conductor = ''.obs;
  final RxString cable = ''.obs;
  final RxString transformerLocation = ''.obs;
  final RxString transformerType = ''.obs;
  final RxString transformerPole = ''.obs;
  final RxString transformerKV = ''.obs;

  final RxMap<String, String> gpsMaterialQuantities = <String, String>{}.obs;

  // Pole Details Screen
  final RxString poleNumber = ''.obs;
  final RxString existingOrNew = ''.obs;
  final RxString poleDescription = ''.obs;
  final RxString poleType = ''.obs;
  final RxString poleSize = ''.obs;
  final RxString poleStructure = ''.obs;

  final RxMap<String, String> poleMaterialQuantities = <String, String>{}.obs;

  void clearAll() {
    // Waypoint Screen
    waypointName.value = '';
    waypointDescription.value = '';
    waypointDistance.value = '';
    waypointLatitude.value = '';
    waypointLongitude.value = '';
    routeType.value = '';
    routeStartingPoint.value = '';
    routeEndingPoint.value = '';

    // Gps Details Screen
    userId.value = '';
    dateTime.value = '';
    district.value = '';
    routeStartPoint.value = '';
    length.value = '';
    gpsCoordinatesStartPoint.value = '';
    coordinatesCurrentWaypoint.value = '';
    substationName.value = '';
    feederName.value = '';
    conductor.value = '';
    cable.value = '';
    transformerLocation.value = '';
    transformerType.value = '';
    transformerPole.value = '';
    transformerKV.value = '';
    gpsMaterialQuantities.clear();

    // Pole Details Screen
    poleNumber.value = '';
    existingOrNew.value = '';
    poleDescription.value = '';
    poleType.value = '';
    poleSize.value = '';
    poleStructure.value = '';
    poleMaterialQuantities.clear();
  }
}