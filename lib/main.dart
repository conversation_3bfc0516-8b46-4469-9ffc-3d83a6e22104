import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/common/utils/shared_preferences.dart';
import 'package:ems/routes/router.dart';
import 'package:ems/screens/login/login_screen.dart';
import 'package:ems/screens/select_categories.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:jwt_decoder/jwt_decoder.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  final bool isLoggedIn = await checkLoginStatus();
  runApp(MyApp(isLoggedIn: isLoggedIn));
}

Future<bool> checkLoginStatus() async {
  String? token = await TokenStorage.getToken();
  if (token == null) return false;
  if (JwtDecoder.isExpired(token)) {
    await TokenStorage.deleteToken();
    return false;
  }
  return true;
}

class MyApp extends StatelessWidget {
  final bool isLoggedIn;
  const MyApp({super.key, required this.isLoggedIn});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'AARY',
      debugShowCheckedModeBanner: false,
      //initialRoute: loginScreen,
      onGenerateRoute: (settings) => generateRoutes(settings),
      theme: ThemeData(
        scaffoldBackgroundColor: AppColors.white,
        colorScheme: ColorScheme.fromSeed(seedColor: AppColors.primaryOrange),
      ),
      home: isLoggedIn ? SelectCategories() : LoginScreen(),
    );
  }
}
