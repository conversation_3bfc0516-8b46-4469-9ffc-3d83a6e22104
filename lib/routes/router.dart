import 'package:ems/screens/fibre_optics_screen.dart';
import 'package:ems/screens/gas_screen.dart';
import 'package:ems/screens/history_screen/history_screen.dart';
import 'package:ems/screens/login/login_screen.dart';
import 'package:ems/screens/select_categories.dart';
import 'package:flutter/material.dart';

const String loginScreen = '/loginScreen';
const String gasScreen = '/gasScreen';
const String fibreOpticsScreen = '/fibreOpticsScreen';
const String selectCategoriesScreen = '/selectCategoriesScreen';
const String historyScreen = '/historyScreen';

Route<dynamic> generateRoutes(RouteSettings settings) {
  switch (settings.name) {
    case loginScreen:
      return MaterialPageRoute(builder: (_) => LoginScreen());
    case gasScreen:
      return MaterialPageRoute(builder: (_) => GasScreen());
    case selectCategoriesScreen:
      return MaterialPageRoute(builder: (_) => SelectCategories());
    case fibreOpticsScreen:
      return MaterialPageRoute(builder: (_) => FibreOpticsScreen());
    case historyScreen:
      return MaterialPageRoute(builder: (_) => HistoryScreen());
    default:
      return MaterialPageRoute(
        builder:
            (_) => Scaffold(body: Center(child: Text('404 - Page Not Found'))),
      );
  }
}
