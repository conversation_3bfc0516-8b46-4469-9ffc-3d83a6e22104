import 'dart:async';
import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/common/constants/size.dart';
import 'package:ems/common/widgets/custom_app_bar.dart';
import 'package:ems/common/widgets/custom_circularprogressindicator.dart';
import 'package:ems/common/widgets/custom_round_button.dart';
import 'package:ems/models/project_model.dart';
import 'package:ems/screens/map_screen/map_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MapScreen extends StatefulWidget {
  final Projects project;
  final VoidCallback onBack;

  const MapScreen({super.key, required this.project, required this.onBack});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final Completer<GoogleMapController> _controller = Completer();
  final mapController = Get.put(MapController());

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  Future<void> _initializeMap() async {
    try {
      await mapController.fetchWaypointsFromApi(widget.project.projectId ?? '');
      await mapController.fetchDeviceLocation();
      mapController.renderWaypointsOnMap();
    } catch (e) {
      debugPrint('Error initializing map: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CustomAppBar(
        title: "",
        onPressed: widget.onBack,
        backgroundColor: Colors.transparent,
        textColor: AppColors.black,
      ),
      body: Obx(() {
        final location = mapController.currentLocation.value;
        if (location == null) {
          return const Center(child: CustomCircularProgressIndicator());
        }

        return Stack(
          children: [
            GoogleMap(
              initialCameraPosition: CameraPosition(
                target: location,
                zoom: 16.0,
              ),
              myLocationEnabled: mapController.hasLocationPermission.value,
              myLocationButtonEnabled: false,
              zoomControlsEnabled: false,
              mapType: mapController.currentMapType.value,
              markers: mapController.markers,
              polylines: mapController.polylines,
              onMapCreated: (GoogleMapController controller) {
                if (!_controller.isCompleted) {
                  _controller.complete(controller);
                  mapController.googleMapController.value = controller;
                  mapController.updateCameraPosition(location);
                  mapController.isMapLoaded.value = true;
                }
              },
              gestureRecognizers: {},
            ),

            if (mapController.isMapLoaded.value) ...[
              Positioned(
                top: MediaQuery.of(context).padding.top + kToolbarHeight,
                right: 16,
                child: Column(
                  children: [
                    roundButton(
                      icon: Icons.layers,
                      onPressed: () {
                        mapController.toggleMapType();
                      },
                    ),
                    1.5.ph,
                    roundButton(
                      icon: Icons.my_location,
                      onPressed: () async {
                        await mapController.fetchDeviceLocation();
                        final controller = await _controller.future;
                        final location = mapController.currentLocation.value;
                        if (location != null) {
                          controller.animateCamera(
                            CameraUpdate.newLatLngZoom(location, 16.0),
                          );
                        }
                      },
                    ),
                    1.5.ph,
                    Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFF4A3C2F),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black12,
                            blurRadius: 6,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          roundButton(
                            icon: Icons.add,
                            onPressed: () async {
                              final controller = await _controller.future;
                              controller.animateCamera(CameraUpdate.zoomIn());
                            },
                          ),
                          1.ph,
                          roundButton(
                            icon: Icons.remove,
                            onPressed: () async {
                              final controller = await _controller.future;
                              controller.animateCamera(CameraUpdate.zoomOut());
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        );
      }),
    );
  }
}
