import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/common/constants/size.dart';
import 'package:ems/common/widgets/custom_app_bar.dart';
import 'package:ems/common/widgets/custom_card.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'history_screen_controller.dart';

class HistoryScreen extends StatelessWidget {
  const HistoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(HistoryScreenController());
    controller.fetchWaypoints();

    return Scaffold(
      backgroundColor: Colors.white,
      extendBodyBehindAppBar: false,
      appBar: CustomAppBar(
        showBackButton: true,
        backButtonColor: Colors.black,
        onPressed: Get.back,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Material(
        color: Colors.white,
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.routeList.isEmpty) {
            return const Center(child: Text('No routes found'));
          }

          final Map<String, List<Map<String, dynamic>>> groupedRoutes = {};
          for (var route in controller.routeList) {
            final date = controller.getFormattedDate(
              controller.routeList.indexOf(route),
            );
            groupedRoutes.putIfAbsent(date, () => []).add(route);
          }

          return Container(
            color: Colors.white,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              physics: const ClampingScrollPhysics(),
              child: Column(
                children: [
                  for (var entry in groupedRoutes.entries)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0, top: 8.0),
                          child: Text(
                            entry.key,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        2.5.ph,
                        for (var route in entry.value)
                          CustomCard(
                            routeName:
                                '${route['feederName']} - Feeder',
                            onTap:
                                () => controller.onRouteCardTap(
                                  controller.routeList.indexOf(route),
                                ),
                            icon: Icons.info_outline,
                            iconColor: AppColors.black,
                            cardColor: AppColors.cardBlue,
                            borderRadius: 24,
                          ),
                      ],
                    ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }
}
