import 'package:dio/dio.dart';
import 'package:ems/common/constants/general.dart';
import 'package:ems/common/utils/clear_all_controllers.dart';
import 'package:ems/common/utils/clear_values_util.dart';
import 'package:ems/common/utils/distance_calculator.dart';
import 'package:ems/common/utils/shared_preferences.dart';
import 'package:ems/common/utils/upload_data_controller.dart';
import 'package:ems/common/utils/validators.dart';
import 'package:ems/screens/Bottom_Nav_Bar/nav_controller.dart';
import 'package:ems/screens/home/<USER>';
import 'package:ems/screens/pole_details_screen/pole_details_screen.dart';
import 'package:ems/screens/upload_image_screen/upload_image_screen.dart';
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';
import 'package:location/location.dart' as loc;
import 'package:intl/intl.dart';

/// GpsDetailsController manages the GPS and material details collection functionality
///
/// This controller is responsible for:
/// - Managing a large number of form fields for GPS and material data entry
/// - Handling device location services and permissions
/// - Auto-filling GPS coordinates and location information
/// - Calculating distances between waypoints
/// - Validating form data before submission
/// - Storing collected data for upload
/// - Managing default values based on transformer KV selection
/// - Clearing form data when needed
///
/// It uses GetX for reactive state management and integrates with location services
/// to provide accurate GPS coordinates and distance calculations.
class GpsDetailsController extends GetxController {
  final timeDateController = TextEditingController();
  final userIdController = TextEditingController();
  final districtController = TextEditingController();
  final routeStartPointController = TextEditingController();
  final lengthController = TextEditingController();
  final gpsCoordinatesStartPointController = TextEditingController();
  final coorddinatesCurrentWaypointController = TextEditingController();
  final substationNameController = TextEditingController();
  final feederNameController = TextEditingController();
  final conductorController = TextEditingController();
  final cableController = TextEditingController();
  final transformerLocationController = TextEditingController();
  final transformerTypeController = TextEditingController();
  final transformerPoleController = TextEditingController();
  final transformerKVController = TextEditingController();
  final distributionBoxController = TextEditingController();
  final abSwithController = TextEditingController();
  final anchorRodController = TextEditingController();
  final anchoringAssemblyController = TextEditingController();
  final angle4FeetController = TextEditingController();
  final angle9FeetController = TextEditingController();
  final basePlatController = TextEditingController();
  final channel4FeetController = TextEditingController();
  final channel9FeetController = TextEditingController();
  final doChannelController = TextEditingController();
  final doChannelBackClampController = TextEditingController();
  final doFuseController = TextEditingController();
  final discHardwareController = TextEditingController();
  final discInsulatorPolymericController = TextEditingController();
  final discInsulatorPorcelainController = TextEditingController();
  final dtrBaseChannelController = TextEditingController();
  final dtrSpottingAngleController = TextEditingController();
  final dvcConductorController = TextEditingController();
  final earthingConductorController = TextEditingController();
  final elbowController = TextEditingController();
  final eyeBoltController = TextEditingController();
  final giPinController = TextEditingController();
  final giPipeController = TextEditingController();
  final greeperController = TextEditingController();
  final guyInsulatorController = TextEditingController();
  final iHuckClampController = TextEditingController();
  final lightingArrestorController = TextEditingController();
  final pinInsulatorPolymericController = TextEditingController();
  final pinInsulatorPorcelainController = TextEditingController();
  final poleEarthingController = TextEditingController();
  final sideClampController = TextEditingController();
  final spottingAngleController = TextEditingController();
  final spottingChannelController = TextEditingController();
  final stayClampController = TextEditingController();
  final stayInsulatorController = TextEditingController();
  final stayRoadController = TextEditingController();
  final stayWire712Controller = TextEditingController();
  final suspensionAssemblyClampController = TextEditingController();
  final topChannelController = TextEditingController();
  final topClampController = TextEditingController();
  final turnBuckleController = TextEditingController();
  final vCrossArmController = TextEditingController();
  final vCrossBackClampController = TextEditingController();
  final xBressingController = TextEditingController();
  final earthingCoilController = TextEditingController();

  final loc.Location location = loc.Location();

  final RxString length = ''.obs;
  final RxString userId = ''.obs;
  final RxString district = ''.obs;
  final RxString gpsCoordinatesStartPoint = ''.obs;
  final RxString coorddinatesCurrentWaypoint = ''.obs;
  final RxString feederName = ''.obs;

  final RxString transformerType = 'Existing'.obs;
  final RxString transformerPole = 'Line Pole'.obs;
  final RxString distributionBox = ''.obs;
  final RxString abSwitch = ''.obs;
  final RxString anchorRod = ''.obs;
  final RxString anchoringAssembly = ''.obs;
  final RxString angle4Feet = ''.obs;
  final RxString angle9Feet = ''.obs;
  final RxString basePlat = ''.obs;
  final RxString channel4Feet = ''.obs;
  final RxString channel9Feet = ''.obs;
  final RxString doChannel = ''.obs;
  final RxString doChannelBackClamp = ''.obs;
  final RxString doFuse = ''.obs;
  final RxString discHardware = ''.obs;
  final RxString discInsulatorPloymeric = ''.obs;
  final RxString discInsulatorPorcelain = ''.obs;
  final RxString dtrBaseChannel = ''.obs;
  final RxString dtrSpottingAngle = ''.obs;
  final RxString dtrSpottingAngleWithClamp = ''.obs;
  final RxString dvcConductor = ''.obs;
  final RxString earthingConductor = ''.obs;
  final RxString elbow = ''.obs;
  final RxString eyeBolt = ''.obs;
  final RxString giPin = ''.obs;
  final RxString giPipe = ''.obs;
  final RxString greeper = ''.obs;
  final RxString guyInsulator = ''.obs;
  final RxString iHuckClamp = ''.obs;
  final RxString lightingArrestor = ''.obs;
  final RxString pinInsulatorPolymeric = ''.obs;
  final RxString pinInsulatorPorcelain = ''.obs;
  final RxString poleEarthing = ''.obs;
  final RxString sideClamp = ''.obs;
  final RxString spottingAngle = ''.obs;
  final RxString spottingChannel = ''.obs;
  final RxString stayClamp = ''.obs;
  final RxString stayInsulator = ''.obs;
  final RxString stayRoad = ''.obs;
  final RxString stayWire = ''.obs;
  final RxString suspensionAssemblyClamp = ''.obs;
  final RxString topChannel = ''.obs;
  final RxString topClamp = ''.obs;
  final RxString turnBuckle = ''.obs;
  final RxString vCrossArm = ''.obs;
  final RxString vCrossBackClamp = ''.obs;
  final RxString xBressing = ''.obs;
  final RxString earthingCoil = ''.obs;

  final RxBool isFetchingUserId = true.obs;
  final RxBool isFetchingDistrict = true.obs;
  final RxBool isFetchingLength = true.obs;
  final RxBool showOptionalField = false.obs;
  final RxBool showFieldsBelowConductor = true.obs;

  var dioClient = Dio();

  final navController = Get.find<NavController>();

  final Map<String, RxString> fieldControllers = {};

  /// Initializes the controller when it's first created
  ///
  /// This method:
  /// 1. Sets the current date and time in the appropriate field
  /// 2. Fetches and sets the user ID from the API
  /// 3. Initializes location data including coordinates and distance calculation
  /// 4. Sets up a listener for the length controller to update the reactive variable
  /// 5. Populates the fieldControllers map with all material quantity reactive variables
  ///
  /// The fieldControllers map is used to manage all the material quantity fields
  /// in a more maintainable way, allowing for batch operations on these fields.
  @override
  void onInit() {
    super.onInit();
    setCurrentDateTime();
    setUserId();
    fetchFeederName();
    initializeLocationData();

    lengthController.addListener(() {
      length.value = lengthController.text;
    });

    fieldControllers.addAll({
      'distributionBox': distributionBox,
      'abSwitch': abSwitch,
      'anchorRod': anchorRod,
      'anchoringAssembly': anchoringAssembly,
      'angle4Feet': angle4Feet,
      'angle9Feet': angle9Feet,
      'basePlat': basePlat,
      'channel4Feet': channel4Feet,
      'channel9Feet': channel9Feet,
      'doChannel': doChannel,
      'doChannelBackClamp': doChannelBackClamp,
      'doFuse': doFuse,
      'discHardware': discHardware,
      'discInsulatorPloymeric': discInsulatorPloymeric,
      'discInsulatorPorcelain': discInsulatorPorcelain,
      'dtrBaseChannel': dtrBaseChannel,
      'dtrSpottingAngle': dtrSpottingAngle,
      'dtrSpottingAngleWithClamp': dtrSpottingAngleWithClamp,
      'dvcConductor': dvcConductor,
      'earthingConductor': earthingConductor,
      'elbow': elbow,
      'eyeBolt': eyeBolt,
      'giPin': giPin,
      'giPipe': giPipe,
      'greeper': greeper,
      'guyInsulator': guyInsulator,
      'iHuckClamp': iHuckClamp,
      'lightingArrestor': lightingArrestor,
      'pinInsulatorPolymeric': pinInsulatorPolymeric,
      'pinInsulatorPorcelain': pinInsulatorPorcelain,
      'poleEarthing': poleEarthing,
      'sideClamp': sideClamp,
      'spottingAngle': spottingAngle,
      'spottingChannel': spottingChannel,
      'stayClamp': stayClamp,
      'stayInsulator': stayInsulator,
      'stayRoad': stayRoad,
      'stayWire': stayWire,
      'suspensionAssemblyClamp': suspensionAssemblyClamp,
      'topChannel': topChannel,
      'topClamp': topClamp,
      'turnBuckle': turnBuckle,
      'vCrossArm': vCrossArm,
      'vCrossBackClamp': vCrossBackClamp,
      'xBressing': xBressing,
      'earthingCoil': earthingCoil,
    });
  }

  /// Initializes location data with proper sequencing
  ///
  /// This method:
  /// 1. Auto-fills the GPS coordinates start point based on available data
  /// 2. Adds a small delay to ensure coordinates are properly set
  /// 3. Calculates the distance from the previous waypoint
  /// 4. Handles any errors that might occur during the process
  ///
  /// The delay is necessary to ensure that the coordinates are properly set
  /// before attempting to calculate distances.
  Future<void> initializeLocationData() async {
    try {
      await autoFillGpsCoordsStartPoint();

      await Future.delayed(const Duration(milliseconds: 500));

      await calculateDistanceFromPreviousWaypoint();
    } catch (e) {
      debugPrint('Error in location initialization: $e');
    }
  }

  // Default values of text fields
  final Map<String, Map<String, String>> kvDefaults = {
    '5KV': {
      'distributionBox': '1',
      'abSwitch': '0',
      'anchorRod': '2',
      'anchoringAssembly': '1',
      'angle4Feet': '4',
      'angle9Feet': '6',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '2',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '3',
      'discHardware': '3',
      'discInsulatorPloymeric': '3',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '2',
      'dtrSpottingAngle': '1',
      'dtrSpottingAngleWithClamp': '4',
      'dvcConductor': '7',
      'earthingConductor': '1',
      'elbow': '0',
      'eyeBolt': '1',
      'giPin': '1',
      'giPipe': '3',
      'greeper': '0',
      'guyInsulator': '2',
      'iHuckClamp': '0',
      'lightingArrestor': '3',
      'pinInsulatorPolymeric': '12',
      'pinInsulatorPorcelain': '0',
      'poleEarthing': '3',
      'sideClamp': '18',
      'spottingAngle': '2',
      'spottingChannel': '0',
      'stayClamp': '2',
      'stayInsulator': '2',
      'stayRoad': '2',
      'stayWire': '0',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '2',
      'vCrossArm': '2',
      'vCrossBackClamp': '0',
      'xBressing': '0',
      'earthingCoil': '3',
    },
    '10KV': {
      'distributionBox': '1',
      'abSwitch': '0',
      'anchorRod': '2',
      'anchoringAssembly': '1',
      'angle4Feet': '4',
      'angle9Feet': '6',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '2',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '3',
      'discHardware': '3',
      'discInsulatorPloymeric': '3',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '2',
      'dtrSpottingAngle': '1',
      'dvcConductor': '4',
      'earthingConductor': '7',
      'elbow': '1',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '3',
      'guyInsulator': '0',
      'iHuckClamp': '2',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '12',
      'poleEarthing': '0',
      'sideClamp': '3',
      'spottingAngle': '18',
      'spottingChannel': '2',
      'stayClamp': '0',
      'stayInsulator': '2',
      'stayRoad': '2',
      'stayWire': '2',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '0',
      'vCrossArm': '2',
      'vCrossBackClamp': '2',
      'xBressing': '0',
      'earthingCoil': '0',
    },
    '25KV': {
      'distributionBox': '1',
      'abSwitch': '0',
      'anchorRod': '2',
      'anchoringAssembly': '1',
      'angle4Feet': '4',
      'angle9Feet': '6',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '2',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '3',
      'discHardware': '3',
      'discInsulatorPloymeric': '3',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '2',
      'dtrSpottingAngle': '1',
      'dvcConductor': '4',
      'earthingConductor': '7',
      'elbow': '1',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '3',
      'guyInsulator': '0',
      'iHuckClamp': '2',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '12',
      'poleEarthing': '0',
      'sideClamp': '3',
      'spottingAngle': '18',
      'spottingChannel': '2',
      'stayClamp': '0',
      'stayInsulator': '2',
      'stayRoad': '2',
      'stayWire': '2',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '0',
      'vCrossArm': '2',
      'vCrossBackClamp': '2',
      'xBressing': '0',
      'earthingCoil': '0',
    },
    '100KV': {
      'distributionBox': '1',
      'abSwitch': '0',
      'anchorRod': '2',
      'anchoringAssembly': '1',
      'angle4Feet': '4',
      'angle9Feet': '6',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '2',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '3',
      'discHardware': '3',
      'discInsulatorPloymeric': '3',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '2',
      'dtrSpottingAngle': '1',
      'dvcConductor': '4',
      'earthingConductor': '7',
      'elbow': '1',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '3',
      'guyInsulator': '0',
      'iHuckClamp': '2',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '12',
      'poleEarthing': '0',
      'sideClamp': '3',
      'spottingAngle': '18',
      'spottingChannel': '2',
      'stayClamp': '0',
      'stayInsulator': '2',
      'stayRoad': '2',
      'stayWire': '2',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '0',
      'vCrossArm': '2',
      'vCrossBackClamp': '2',
      'xBressing': '0',
      'earthingCoil': '0',
    },
    '200KV': {
      'distributionBox': '1',
      'abSwitch': '0',
      'anchorRod': '2',
      'anchoringAssembly': '1',
      'angle4Feet': '4',
      'angle9Feet': '6',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '2',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '3',
      'discHardware': '3',
      'discInsulatorPloymeric': '3',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '2',
      'dtrSpottingAngle': '1',
      'dvcConductor': '4',
      'earthingConductor': '7',
      'elbow': '1',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '3',
      'guyInsulator': '0',
      'iHuckClamp': '2',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '12',
      'poleEarthing': '0',
      'sideClamp': '3',
      'spottingAngle': '18',
      'spottingChannel': '2',
      'stayClamp': '0',
      'stayInsulator': '2',
      'stayRoad': '2',
      'stayWire': '2',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '0',
      'vCrossArm': '2',
      'vCrossBackClamp': '2',
      'xBressing': '0',
      'earthingCoil': '0',
    },
    '250KV': {
      'distributionBox': '1',
      'abSwitch': '0',
      'anchorRod': '2',
      'anchoringAssembly': '1',
      'angle4Feet': '4',
      'angle9Feet': '6',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '2',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '3',
      'discHardware': '3',
      'discInsulatorPloymeric': '3',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '2',
      'dtrSpottingAngle': '1',
      'dvcConductor': '4',
      'earthingConductor': '7',
      'elbow': '1',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '3',
      'guyInsulator': '0',
      'iHuckClamp': '2',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '12',
      'poleEarthing': '0',
      'sideClamp': '3',
      'spottingAngle': '18',
      'spottingChannel': '2',
      'stayClamp': '0',
      'stayInsulator': '2',
      'stayRoad': '2',
      'stayWire': '2',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '0',
      'vCrossArm': '2',
      'vCrossBackClamp': '2',
      'xBressing': '0',
      'earthingCoil': '0',
    },
    '315KV': {
      'distributionBox': '1',
      'abSwitch': '0',
      'anchorRod': '2',
      'anchoringAssembly': '1',
      'angle4Feet': '4',
      'angle9Feet': '6',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '2',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '3',
      'discHardware': '3',
      'discInsulatorPloymeric': '3',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '2',
      'dtrSpottingAngle': '1',
      'dvcConductor': '4',
      'earthingConductor': '7',
      'elbow': '1',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '3',
      'guyInsulator': '0',
      'iHuckClamp': '2',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '12',
      'poleEarthing': '0',
      'sideClamp': '3',
      'spottingAngle': '18',
      'spottingChannel': '2',
      'stayClamp': '0',
      'stayInsulator': '2',
      'stayRoad': '2',
      'stayWire': '2',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '0',
      'vCrossArm': '2',
      'vCrossBackClamp': '2',
      'xBressing': '0',
      'earthingCoil': '0',
    },
    '400KV': {
      'distributionBox': '1',
      'abSwitch': '0',
      'anchorRod': '2',
      'anchoringAssembly': '1',
      'angle4Feet': '4',
      'angle9Feet': '6',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '2',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '3',
      'discHardware': '3',
      'discInsulatorPloymeric': '3',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '2',
      'dtrSpottingAngle': '1',
      'dvcConductor': '4',
      'earthingConductor': '7',
      'elbow': '1',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '3',
      'guyInsulator': '0',
      'iHuckClamp': '2',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '12',
      'poleEarthing': '0',
      'sideClamp': '3',
      'spottingAngle': '18',
      'spottingChannel': '2',
      'stayClamp': '0',
      'stayInsulator': '2',
      'stayRoad': '2',
      'stayWire': '2',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '0',
      'vCrossArm': '2',
      'vCrossBackClamp': '2',
      'xBressing': '0',
      'earthingCoil': '0',
    },
    '500KV': {
      'distributionBox': '1',
      'abSwitch': '0',
      'anchorRod': '2',
      'anchoringAssembly': '1',
      'angle4Feet': '4',
      'angle9Feet': '6',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '2',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '3',
      'discHardware': '3',
      'discInsulatorPloymeric': '3',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '2',
      'dtrSpottingAngle': '1',
      'dvcConductor': '4',
      'earthingConductor': '7',
      'elbow': '1',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '3',
      'guyInsulator': '0',
      'iHuckClamp': '2',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '12',
      'poleEarthing': '0',
      'sideClamp': '3',
      'spottingAngle': '18',
      'spottingChannel': '2',
      'stayClamp': '0',
      'stayInsulator': '2',
      'stayRoad': '2',
      'stayWire': '2',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '0',
      'vCrossArm': '2',
      'vCrossBackClamp': '2',
      'xBressing': '0',
      'earthingCoil': '0',
    },
    'Other': {
      'distributionBox': '1',
      'abSwitch': '0',
      'anchorRod': '2',
      'anchoringAssembly': '1',
      'angle4Feet': '4',
      'angle9Feet': '6',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '2',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '3',
      'discHardware': '3',
      'discInsulatorPloymeric': '3',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '2',
      'dtrSpottingAngle': '1',
      'dvcConductor': '4',
      'earthingConductor': '7',
      'elbow': '1',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '3',
      'guyInsulator': '0',
      'iHuckClamp': '2',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '12',
      'poleEarthing': '0',
      'sideClamp': '3',
      'spottingAngle': '18',
      'spottingChannel': '2',
      'stayClamp': '0',
      'stayInsulator': '2',
      'stayRoad': '2',
      'stayWire': '2',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '0',
      'vCrossArm': '2',
      'vCrossBackClamp': '2',
      'xBressing': '0',
      'earthingCoil': '0',
    },
  };

  /// Sets the current date and time in the date-time field
  ///
  /// This method:
  /// 1. Gets the current date and time
  /// 2. Formats it using the pattern 'dd-MM-yyyy, h:mma'
  /// 3. Sets the formatted string to the timeDateController
  ///
  /// This ensures that the form always shows the current date and time
  /// when it's opened, providing accurate timestamp information.
  void setCurrentDateTime() {
    final now = DateTime.now();
    final formatted = DateFormat('dd-MM-yyyy, h:mma').format(now);
    timeDateController.text = formatted;
  }

  /// Fetches and sets the user ID from the API
  ///
  /// This method:
  /// 1. Retrieves the authentication token from storage
  /// 2. Sets the isFetchingUserId flag to true to indicate loading state
  /// 3. Makes an API request to get the current user's information
  /// 4. Extracts the employee ID from the response
  /// 5. Updates both the userIdController and userId reactive variable
  /// 6. Handles any errors that might occur during the process
  /// 7. Sets the isFetchingUserId flag to false when complete
  ///
  /// This ensures that the form is pre-filled with the current user's ID,
  /// improving data accuracy and user experience.
  Future<void> setUserId() async {
    String? authToken = await TokenStorage.getToken();

    try {
      isFetchingUserId.value = true;

      final url = '$baseUrl/api/auth/me';
      final response = await dioClient.get(
        url,
        options: Options(headers: {'Authorization': 'Bearer $authToken'}),
      );

      if (response.statusCode == 200 && response.data['success'] == true) {
        final user = response.data['user'];
        final empId = user['empId'];

        if (empId != null) {
          userIdController.text = empId;
          userId.value = empId;
        }
      } else {
        debugPrint(
          'Failed to fetch user. Status: ${response.statusCode}, Body: ${response.data}',
        );
      }
    } finally {
      isFetchingUserId.value = false;
    }
  }

  /// Sets both GPS coordinates (start and current) and district from current location
  ///
  /// This method:
  /// 1. Sets the isFetchingDistrict flag to true to indicate loading state
  /// 2. Ensures location services are enabled and permissions are granted
  /// 3. Gets the current device location
  /// 4. Formats the coordinates as a string
  /// 5. Updates both start point and current waypoint coordinates
  /// 6. Fetches and sets the district based on the coordinates
  /// 7. Handles any errors that might occur during the process
  /// 8. Sets the isFetchingDistrict flag to false when complete
  ///
  /// This is used when both start and current coordinates need to be set
  /// to the same location, typically when starting a new tracking session.
  Future<void> setBothCoordinatesAndDistrictFromCurrentLocation() async {
    try {
      isFetchingDistrict.value = true;

      await _ensureLocationServicesAndPermission();

      final currentLocation = await location.getLocation();
      final formattedCoords =
          '${currentLocation.latitude}, ${currentLocation.longitude}';

      gpsCoordinatesStartPoint.value = formattedCoords;
      coorddinatesCurrentWaypoint.value = formattedCoords;

      gpsCoordinatesStartPointController.text = formattedCoords;
      coorddinatesCurrentWaypointController.text = formattedCoords;

      await _fetchAndSetDistrict(
        currentLocation.latitude!,
        currentLocation.longitude!,
      );
    } catch (e) {
      debugPrint('Error setting coordinates/district: $e');
    } finally {
      isFetchingDistrict.value = false;
    }
  }

  /// Sets only the current waypoint coordinates and district from current location
  ///
  /// This method:
  /// 1. Sets the isFetchingDistrict flag to true to indicate loading state
  /// 2. Ensures location services are enabled and permissions are granted
  /// 3. Gets the current device location
  /// 4. Formats the coordinates as a string
  /// 5. Updates only the current waypoint coordinates (not the start point)
  /// 6. Fetches and sets the district based on the coordinates
  /// 7. Handles any errors that might occur during the process
  /// 8. Sets the isFetchingDistrict flag to false when complete
  ///
  /// This is used when only the current waypoint coordinates need to be updated,
  /// typically when adding a new waypoint to an existing tracking session.
  Future<void> setDistrictAndCoordsCurrentWaypointFromCurrentLocation() async {
    try {
      isFetchingDistrict.value = true;

      await _ensureLocationServicesAndPermission();

      final currentLocation = await location.getLocation();

      final formattedCoords =
          '${currentLocation.latitude}, ${currentLocation.longitude}';

      coorddinatesCurrentWaypoint.value = formattedCoords;
      coorddinatesCurrentWaypointController.text = formattedCoords;

      await _fetchAndSetDistrict(
        currentLocation.latitude!,
        currentLocation.longitude!,
      );
    } catch (e) {
      debugPrint('Error fetching district: $e');
    } finally {
      isFetchingDistrict.value = false;
    }
  }

  /// Ensures location services are enabled and permissions are granted
  ///
  /// This method:
  /// 1. Checks if location services are enabled on the device
  /// 2. If not enabled, requests the user to enable them
  /// 3. Throws an exception if services cannot be enabled
  /// 4. Checks if location permissions are granted
  /// 5. If not granted, requests the permissions from the user
  /// 6. Throws an exception if permissions cannot be granted
  ///
  /// This is a helper method used by other location-related functions
  /// to ensure the necessary prerequisites are met before attempting
  /// to access the device's location.
  Future<void> _ensureLocationServicesAndPermission() async {
    bool serviceEnabled = await location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await location.requestService();
      if (!serviceEnabled) throw Exception("Location services not enabled");
    }

    loc.PermissionStatus permissionGranted = await location.hasPermission();
    if (permissionGranted == loc.PermissionStatus.denied) {
      permissionGranted = await location.requestPermission();
      if (permissionGranted != loc.PermissionStatus.granted) {
        throw Exception("Location permission not granted");
      }
    }
  }

  /// Fetches and sets the district name based on coordinates
  ///
  /// This method:
  /// 1. Uses the geocoding package to convert coordinates to address information
  /// 2. Extracts the locality (district) from the first placemark
  /// 3. Updates both the districtController and district reactive variable
  ///
  /// This is a helper method used by other location-related functions
  /// to determine the district name based on GPS coordinates.
  ///
  /// @param lat The latitude coordinate
  /// @param lon The longitude coordinate
  Future<void> _fetchAndSetDistrict(double lat, double lon) async {
    final placemarks = await placemarkFromCoordinates(lat, lon);
    if (placemarks.isNotEmpty) {
      final fetchedDistrict = placemarks.first.locality;
      if (fetchedDistrict != null && fetchedDistrict.isNotEmpty) {
        districtController.text = fetchedDistrict;
        district.value = fetchedDistrict;
      }
    }
  }

  /// Updates material quantity fields based on the selected transformer KV
  ///
  /// This method:
  /// 1. Retrieves the default values for the selected KV from the kvDefaults map
  /// 2. If no defaults are found, returns without making changes
  /// 3. Updates all material quantity fields with the default values
  /// 4. Shows or hides optional fields based on the selected KV
  ///
  /// This provides a convenient way to pre-fill the form with appropriate
  /// default values based on the transformer KV selection, improving
  /// data entry efficiency and accuracy.
  ///
  /// @param kv The selected transformer KV value (e.g., '5KV', '10KV', etc.)
  void updateFieldsForTransformerKV(String kv) {
    final defaults = kvDefaults[kv];
    if (defaults == null) return;

    for (var entry in defaults.entries) {
      fieldControllers[entry.key]?.value = entry.value;
    }

    showOptionalField.value = kv == '5KV';
  }

  /// Updates the visibility of fields below Conductor based on the selected value
  ///
  /// This method:
  /// 1. Checks if the selected conductor value is "Not Applicable"
  /// 2. If it is, hides all fields below Conductor by setting showFieldsBelowConductor to false
  /// 3. If it's any other value, shows all fields by setting showFieldsBelowConductor to true
  ///
  /// @param conductorValue The selected value from the Conductor dropdown
  void updateFieldsVisibilityBasedOnConductor(String? conductorValue) {
    if (conductorValue == 'Not Applicable') {
      showFieldsBelowConductor.value = false;
    } else {
      showFieldsBelowConductor.value = true;
    }

    // Update the controller text
    conductorController.text = conductorValue ?? '';
  }

  /// Clears all form fields and resets the form to its initial state
  ///
  /// This method:
  /// 1. Clears all reactive string variables using the ClearRxStringValues utility
  /// 2. Clears all TextEditingController instances using the clearControllers utility
  /// 3. Resets the showOptionalField flag to false
  ///
  /// This comprehensive clearing ensures that all form data is reset
  /// when needed, such as after successful submission or when the user
  /// explicitly requests to clear the form.
  void clearAll() {
    ClearRxStringValues.clearRxStrings([
      userId,
      district,
      gpsCoordinatesStartPoint,
      feederName,
      coorddinatesCurrentWaypoint,
      distributionBox,
      abSwitch,
      anchorRod,
      anchoringAssembly,
      angle4Feet,
      angle9Feet,
      basePlat,
      channel4Feet,
      channel9Feet,
      doChannel,
      doChannelBackClamp,
      doFuse,
      discHardware,
      discInsulatorPloymeric,
      discInsulatorPorcelain,
      dtrBaseChannel,
      dtrSpottingAngle,
      dtrSpottingAngleWithClamp,
      dvcConductor,
      earthingConductor,
      elbow,
      eyeBolt,
      giPin,
      giPipe,
      greeper,
      guyInsulator,
      iHuckClamp,
      lightingArrestor,
      pinInsulatorPolymeric,
      pinInsulatorPorcelain,
      poleEarthing,
      sideClamp,
      spottingAngle,
      spottingChannel,
      stayClamp,
      stayInsulator,
      stayRoad,
      stayWire,
      suspensionAssemblyClamp,
      topChannel,
      topClamp,
      turnBuckle,
      vCrossArm,
      vCrossBackClamp,
      xBressing,
      earthingCoil,
    ]);

    clearControllers([
      timeDateController,
      userIdController,
      districtController,
      routeStartPointController,
      lengthController,
      gpsCoordinatesStartPointController,
      coorddinatesCurrentWaypointController,
      substationNameController,
      feederNameController,
      conductorController,
      cableController,
      transformerLocationController,
      transformerTypeController,
      transformerPoleController,
      transformerKVController,
      distributionBoxController,
      abSwithController,
      anchorRodController,
      anchoringAssemblyController,
      angle4FeetController,
      angle9FeetController,
      basePlatController,
      channel4FeetController,
      channel9FeetController,
      doChannelController,
      doChannelBackClampController,
      doFuseController,
      discHardwareController,
      discInsulatorPolymericController,
      discInsulatorPorcelainController,
      dtrBaseChannelController,
      dtrSpottingAngleController,
      dvcConductorController,
      earthingConductorController,
      elbowController,
      eyeBoltController,
      giPinController,
      giPipeController,
      greeperController,
      guyInsulatorController,
      iHuckClampController,
      lightingArrestorController,
      pinInsulatorPolymericController,
      pinInsulatorPorcelainController,
      poleEarthingController,
      sideClampController,
      spottingAngleController,
      spottingChannelController,
      stayClampController,
      stayInsulatorController,
      stayRoadController,
      stayWire712Controller,
      suspensionAssemblyClampController,
      topChannelController,
      topClampController,
      turnBuckleController,
      vCrossArmController,
      vCrossBackClampController,
      xBressingController,
      earthingCoilController,
    ]);
    showOptionalField.value = false;
  }

  /// Validates all form fields and proceeds to the next screen if valid
  ///
  /// This method:
  /// 1. Creates a list to store validation errors
  /// 2. Gets a reference to the UploadDataController for data transfer
  /// 3. Validates all text input fields using the Validators utility
  /// 4. Validates all material quantity fields with special handling for conditional fields
  /// 5. If any errors are found, displays the first error in a snackbar
  /// 6. If all validations pass:
  ///    a. Transfers all form data to the UploadDataController
  ///    b. Clears the form
  ///    c. Navigates to the PoleDetailsScreen
  ///
  /// This ensures that only valid and complete data is submitted,
  /// improving data quality and preventing incomplete submissions.
  void validateAndProceed() {
    final List<String> errors = [];
    final uploadData = Get.find<UploadDataController>();

    final Map<String, TextEditingController> textControllers = {
      'Date and Time': timeDateController,
      'User ID': userIdController,
      'District': districtController,
      'Route Start Point': routeStartPointController,
      'Length': lengthController,
      'GPS Coordinates Start Point': gpsCoordinatesStartPointController,
      'Coordinates Current Way point': coorddinatesCurrentWaypointController,
      'Substation Name': substationNameController,
      'Feeder Name': feederNameController,
      'Conductor': conductorController,
    };

    textControllers.forEach((label, controller) {
      final result = Validators.validator(controller.text, label);
      if (result != null) errors.add(result);
    });

    // Skip validation for fields below Conductor if "Not Applicable" is selected
    if (conductorController.text != 'Not Applicable') {
      // Only validate fields below Conductor if Conductor is not "Not Applicable"
      fieldControllers.forEach((label, rxValue) {
        if (label == 'dtrSpottingAngleWithClamp' &&
            transformerKVController.text != '5KV') {
          return;
        }
        final result = Validators.validator(rxValue.value, label);
        if (result != null) errors.add(result);
      });

      // Validate additional fields that are not in fieldControllers
      final additionalFields = {
        'Cable': cableController,
        'Transformer Location': transformerLocationController,
        'Transformer Type': transformerTypeController,
        'Transformer Pole': transformerPoleController,
        'Transformer KV': transformerKVController,
      };

      additionalFields.forEach((label, controller) {
        final result = Validators.validator(controller.text, label);
        if (result != null) errors.add(result);
      });
    }

    if (errors.isNotEmpty) {
      Get.snackbar(
        'Empty Fields',
        errors.first,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } else {
      uploadData
        ..dateTime.value = timeDateController.text
        ..userId.value = userIdController.text
        ..district.value = districtController.text
        ..routeStartPoint.value = routeStartPointController.text
        ..length.value = lengthController.text
        ..gpsCoordinatesStartPoint.value =
            gpsCoordinatesStartPointController.text
        ..coordinatesCurrentWaypoint.value =
            coorddinatesCurrentWaypointController.text
        ..substationName.value = substationNameController.text
        ..feederName.value = feederNameController.text
        ..conductor.value = conductorController.text
        ..cable.value = cableController.text
        ..transformerLocation.value = transformerLocationController.text
        ..transformerType.value = transformerTypeController.text
        ..transformerPole.value = transformerPoleController.text
        ..transformerKV.value = transformerKVController.text;

      uploadData.gpsMaterialQuantities.clear();

      // Always transfer data to UploadDataController, even if fields are empty
      // This ensures that all data is available for the next screen
      fieldControllers.forEach((key, rxValue) {
        // If Conductor is "Not Applicable", set empty values for all fields
        if (conductorController.text == 'Not Applicable') {
          uploadData.gpsMaterialQuantities[key] = '';
        } else {
          uploadData.gpsMaterialQuantities[key] = rxValue.value;
        }
      });

      clearAll();

      // If Conductor is "Not Applicable", go to Pole Details Screen
      // Otherwise, go to Image Upload Screen
      if (uploadData.conductor.value == 'Not Applicable') {
        Get.to(() => PoleDetailsScreen());
      } else {
        Get.to(() => const UploadImageScreen());
      }
    }
  }

  /// Auto-fills the GPS coordinates start point based on available data
  ///
  /// This method:
  /// 1. Attempts to get the start coordinates from the NavController
  /// 2. If valid coordinates are found:
  ///    a. Formats them as a string
  ///    b. Updates the gpsCoordinatesStartPoint value and controller
  ///    c. Sets the current waypoint coordinates and district from the current location
  /// 3. If no valid coordinates are found or an error occurs:
  ///    a. Falls back to setting both coordinates and district from the current location
  ///
  /// This provides a seamless experience by automatically filling in
  /// the start coordinates when continuing an existing tracking session.
  Future<void> autoFillGpsCoordsStartPoint() async {
    try {
      final gpsStartCoords = navController.startCoordinates.value;

      if (gpsStartCoords != null) {
        final double latitude = gpsStartCoords['latitude'] ?? 0.0;
        final double longitude = gpsStartCoords['longitude'] ?? 0.0;

        if (latitude != 0.0 || longitude != 0.0) {
          final formattedCoords = '$latitude, $longitude';

          gpsCoordinatesStartPoint.value = formattedCoords;
          gpsCoordinatesStartPointController.text = formattedCoords;

          await setDistrictAndCoordsCurrentWaypointFromCurrentLocation();
          return;
        }
      }

      await setBothCoordinatesAndDistrictFromCurrentLocation();
    } catch (e) {
      await setBothCoordinatesAndDistrictFromCurrentLocation();
    }
  }

  /// Fetches the feederName from the last route's first waypoint
  ///
  /// This method:
  /// 1. Gets the project ID from the NavController
  /// 2. Makes an API request to fetch waypoints for the current project
  /// 3. Checks if there are any waypoint arrays in the response
  /// 4. Gets the last waypoint array (most recent route)
  /// 5. Finds the first waypoint in that array (which has isStart=true)
  /// 6. Extracts the feederName from the gpsDetails of that waypoint
  /// 7. Updates the feederNameController with the fetched value
  /// 8. Handles any errors that might occur during the process
  ///
  /// This ensures that the feederName is consistent across all waypoints in a route.
  Future<void> fetchFeederName() async {
    try {
      // Get the project ID from the NavController
      final homeController = Get.find<HomeController>();
      final projectId = homeController.selectedProject.value?.projectId;

      // Get the authentication token
      final authToken = await TokenStorage.getToken();

      // Check if we have the necessary information
      if (projectId == null || authToken == null) {
        debugPrint("Cannot fetch feederName: Missing project ID or auth token");
        return;
      }

      // Prepare the API request
      final url = '$baseUrl/api/projects/$projectId/waypoints';
      final response = await dioClient.get(
        url,
        options: Options(headers: {'Authorization': 'Bearer $authToken'}),
      );

      // Process the response
      if (response.statusCode == 200 && response.data['success'] == true) {
        final List<dynamic> waypointArrays = response.data['waypoints'];

        // If there are no waypoints, return early
        if (waypointArrays.isEmpty) {
          debugPrint("No waypoints found for this project");
          return;
        }

        // Focus on the last array in the waypoints list (most recent route)
        final lastWaypointArray = waypointArrays.last;

        if (lastWaypointArray is List && lastWaypointArray.isNotEmpty) {
          // Check if the last waypoint in the array has isEnd=true
          final lastWaypoint = lastWaypointArray.last;
          if (lastWaypoint is Map<String, dynamic> && lastWaypoint['isEnd'] == true) {
            debugPrint("Most recent route is already completed (last waypoint has isEnd=true)");
            return; // Route is completed, don't autofill
          }

          // Get the first waypoint in the array (which should have isStart=true)
          final firstWaypoint = lastWaypointArray.first;
          if (firstWaypoint is Map<String, dynamic>) {
            // Check if the waypoint has gpsDetails
            final List<dynamic>? gpsDetails = firstWaypoint['gpsDetails'];
            if (gpsDetails != null && gpsDetails.isNotEmpty) {
              // Get the first gpsDetails entry
              final Map<String, dynamic>? firstGpsDetail = gpsDetails.first;
              if (firstGpsDetail != null) {
                // Extract the feederName
                final String fName = firstGpsDetail['feederName'] ?? '';
                if (fName.isNotEmpty) {
                  // Update the feederNameController
                  feederNameController.text = fName;
                  feederName.value = fName;
                  debugPrint("FeederName fetched successfully: $feederName");
                  return;
                }
              }
            }
          }
        }

        debugPrint("No feederName found in the first waypoint of the most recent route");
      } else {
        debugPrint("Failed to fetch waypoints: ${response.statusCode}");
        debugPrint("Response: ${response.data}");
      }
    } catch (e) {
      debugPrint("Error fetching feederName: $e");
    }
  }

  /// Calculates the distance from the previous waypoint to the current location
  ///
  /// This method:
  /// 1. Sets the isFetchingLength flag to true to indicate loading state
  /// 2. Checks if current waypoint coordinates are available
  /// 3. Retrieves the previous coordinates from the NavController
  /// 4. Handles cases where previous coordinates are not available or invalid
  /// 5. Ensures location services are enabled and permissions are granted
  /// 6. Gets the current device location
  /// 7. Calculates the distance between the previous and current coordinates
  /// 8. Formats the distance and updates the length controller and reactive variable
  /// 9. Sets the isFetchingLength flag to false when complete
  /// 10. Handles any errors that might occur during the process
  ///
  /// This provides accurate distance measurements between waypoints,
  /// which is essential for tracking and reporting purposes.
  Future<void> calculateDistanceFromPreviousWaypoint() async {
    isFetchingLength.value = true;

    try {
      if (coorddinatesCurrentWaypoint.value.isEmpty) {
        return;
      }

      final previousCoords = navController.previousCoordinates.value;

      if (previousCoords == null) {
        lengthController.text = "0 meters";
        length.value = "0 meters";
        isFetchingLength.value = false;
        return;
      }

      final double prevLat = previousCoords['latitude'] ?? 0.0;
      final double prevLng = previousCoords['longitude'] ?? 0.0;

      if (prevLat == 0.0 && prevLng == 0.0) {
        lengthController.text = "0 meters";
        length.value = "0 meters";
        isFetchingLength.value = false;
        return;
      }

      await _ensureLocationServicesAndPermission();
      final currentLocation = await location.getLocation();

      if (currentLocation.latitude == null ||
          currentLocation.longitude == null) {
        return;
      }

      final double currentLat = currentLocation.latitude!;
      final double currentLng = currentLocation.longitude!;

      final distance = await DistanceCalculator.calculateDistance(
        prevLat,
        prevLng,
        currentLat,
        currentLng,
      );

      final formattedDistance = "${distance.toStringAsFixed(0)} meters";
      lengthController.text = formattedDistance;
      length.value = formattedDistance;

      isFetchingLength.value = false;
    } catch (e) {
      debugPrint('Error calculating distance from previous waypoint: $e');
    }
  }
}
