import 'package:ems/common/constants/size.dart';
import 'package:ems/common/widgets/custom_app_bar.dart';
import 'package:ems/common/widgets/custom_button.dart';
import 'package:ems/common/widgets/custom_dropdown_row.dart';
import 'package:ems/common/widgets/custom_header.dart';
import 'package:ems/screens/gps_details_screen/gps_details_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class GpsDetailsScreen extends StatefulWidget {
  const GpsDetailsScreen({super.key});

  @override
  State<GpsDetailsScreen> createState() => _GpsDetailsScreenState();
}

class _GpsDetailsScreenState extends State<GpsDetailsScreen> {
  final GpsDetailsController controller = Get.put(GpsDetailsController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          children: [
            CustomAppBar(
              showBackButton: true,
              onPressed: Get.back,
              backgroundColor: Colors.black,
              backButtonColor: Colors.white,
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 20,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // GPS Details and Pole Details Buttons
                    Row(
                      children: [
                        CustomHeader(title: 'GPS Details', isEnabled: true),
                        1.5.pw,
                        CustomHeader(
                          title: 'Pole Details',
                          isEnabled: false,
                          textColor: Colors.black,
                        ),
                      ],
                    ),
                    2.5.ph,

                    // Textfields and Dropdowns
                    CustomDropdownRow(
                      title: 'Date and Time',
                      items: [],
                      value: controller.timeDateController.text,
                      onChanged:
                          (val) =>
                              controller.timeDateController.text = val ?? '',
                      isEditable: false,
                      showDropdown: false,
                    ),
                    Obx(
                      () => CustomDropdownRow(
                        title: 'User ID',
                        items: [],
                        value: controller.userId.value,
                        onChanged: (val) => controller.userId.value = val ?? '',
                        isEditable: false,
                        showDropdown: false,
                        isLoading: controller.isFetchingUserId.value,
                      ),
                    ),
                    Obx(
                      () => CustomDropdownRow(
                        title: 'District',
                        items: [],
                        value: controller.district.value,
                        onChanged:
                            (val) => controller.district.value = val ?? '',
                        isEditable: false,
                        showDropdown: false,
                        isLoading: controller.isFetchingDistrict.value,
                      ),
                    ),
                    CustomDropdownRow(
                      title: 'Route Start Point',
                      items: [],
                      value: controller.routeStartPointController.text,
                      onChanged:
                          (val) =>
                              controller.routeStartPointController.text =
                                  val ?? '',
                      isEditable: true,
                      showDropdown: false,
                    ),
                    Obx(
                      () => CustomDropdownRow(
                        title: 'Length in meters',
                        items: [],
                        value: controller.length.value,
                        onChanged: (val) {
                          controller.lengthController.text = val ?? '';
                          controller.length.value = val ?? '';
                        },
                        isEditable: true,
                        showDropdown: false,
                        isLoading: controller.isFetchingLength.value,
                      ),
                    ),
                    Obx(
                      () => CustomDropdownRow(
                        title: 'GPS Coordinates Start Point',
                        items: [],
                        value: controller.gpsCoordinatesStartPoint.value,
                        onChanged:
                            (val) =>
                                controller
                                    .gpsCoordinatesStartPointController
                                    .text = val ?? '',
                        isEditable: false,
                        showDropdown: false,
                      ),
                    ),
                    Obx(
                      () => CustomDropdownRow(
                        title: 'Coordinates Current Way Point',
                        items: [],
                        value: controller.coorddinatesCurrentWaypoint.value,
                        onChanged:
                            (val) =>
                                controller
                                    .coorddinatesCurrentWaypointController
                                    .text = val ?? '',
                        isEditable: false,
                        showDropdown: false,
                        isLoading: controller.isFetchingDistrict.value,
                      ),
                    ),
                    CustomDropdownRow(
                      title: 'Substation Name',
                      items: [],
                      value: controller.substationNameController.text,
                      onChanged:
                          (val) =>
                              controller.substationNameController.text =
                                  val ?? '',
                      isEditable: true,
                      showDropdown: false,
                    ),
                    Obx(
                      () => CustomDropdownRow(
                        title: 'Feeder Name',
                        items: [],
                        value: controller.feederName.value,
                        onChanged:
                            (val) =>
                                controller.feederNameController.text =
                                    val ?? '',
                        isEditable: true,
                        showDropdown: false,
                      ),
                    ),

                    CustomDropdownRow(
                      title: 'Conductor',
                      items: [
                        '11KV 34 sq mm AAAC',
                        '11KV 55 sq mm ACSR',
                        '11KV 55 sq mm AAAC',
                        '11KV 100 sq mm AAAC Dog',
                        '11KV 185 sq mm ACSR',
                        'Not Applicable',
                      ],
                      value: controller.conductorController.text,
                      onChanged:
                          (val) => controller
                              .updateFieldsVisibilityBasedOnConductor(val),
                      isEditable: true,
                      showDropdown: true,
                    ),

                    // All fields below Conductor wrapped in a single Obx
                    Obx(
                      () =>
                          controller.showFieldsBelowConductor.value
                              ? Column(
                                children: [
                                  CustomDropdownRow(
                                    title: 'Cable',
                                    items: ['HT Cable', 'LT Cable'],
                                    value: controller.cableController.text,
                                    onChanged:
                                        (val) =>
                                            controller.cableController.text =
                                                val ?? '',
                                    isEditable: false,
                                    showDropdown: true,
                                  ),
                                  CustomDropdownRow(
                                    title: 'Transformer Location',
                                    items: [],
                                    value:
                                        controller
                                            .transformerLocationController
                                            .text,
                                    onChanged:
                                        (val) =>
                                            controller
                                                .transformerLocationController
                                                .text = val ?? '',
                                    isEditable: true,
                                    showDropdown: false,
                                  ),

                                  CustomDropdownRow(
                                    title: 'Transformer Type',
                                    items: ['Existing', 'New'],
                                    value:
                                        controller
                                            .transformerTypeController
                                            .text,
                                    onChanged: (val) {
                                      controller
                                          .transformerTypeController
                                          .text = val ?? '';
                                      controller.transformerType.value =
                                          val ?? 'Existing';
                                    },
                                    showDropdown: true,
                                    isEditable: false,
                                  ),

                                  CustomDropdownRow(
                                    title: 'Transformer Pole',
                                    items: [
                                      'Line Pole',
                                      'Angle Pole',
                                      'Tapping Pole',
                                      'Double Pole Structure',
                                      'Other',
                                    ],
                                    value:
                                        controller
                                            .transformerPoleController
                                            .text,
                                    onChanged: (val) {
                                      controller
                                          .transformerPoleController
                                          .text = val ?? '';
                                      controller.transformerPole.value =
                                          val ?? 'Line Pole';
                                    },
                                    isEditable: true,
                                    showDropdown: true,
                                  ),

                                  CustomDropdownRow(
                                    title: 'Transformer KV',
                                    items: [
                                      '5KV',
                                      '10KV',
                                      '25KV',
                                      '100KV',
                                      '200KV',
                                      '250KV',
                                      '315KV',
                                      '400KV',
                                      '500KV',
                                      'Other',
                                    ],
                                    value:
                                        controller.transformerKVController.text,
                                    onChanged: (val) {
                                      controller.transformerKVController.text =
                                          val ?? '';
                                      controller.updateFieldsForTransformerKV(
                                        val ?? '',
                                      );
                                    },
                                    isEditable: true,
                                    showDropdown: true,
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: '3Phase L.T. Distribution box',
                                      items: [],
                                      value: controller.distributionBox.value,
                                      onChanged:
                                          (val) =>
                                              controller.distributionBox.value =
                                                  val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'AB Switch',
                                      items: [],
                                      value: controller.abSwitch.value,
                                      onChanged:
                                          (val) =>
                                              controller.abSwitch.value =
                                                  val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Anchor Rod',
                                      items: [],
                                      value: controller.anchorRod.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .anchorRodController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Anchoring Assembly',
                                      items: [],
                                      value: controller.anchoringAssembly.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .anchoringAssemblyController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Angle (4 feet)',
                                      items: [],
                                      value: controller.angle4Feet.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .angle4FeetController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Angle (9 feet)',
                                      items: [],
                                      value: controller.angle9Feet.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .angle9FeetController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Base Plat',
                                      items: [],
                                      value: controller.basePlat.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .basePlatController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Channel (4 feet)',
                                      items: [],
                                      value: controller.channel4Feet.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .channel4FeetController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Channel (9 feet)',
                                      items: [],
                                      value: controller.channel9Feet.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .channel9FeetController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'D.O. Channel',
                                      items: [],
                                      value: controller.doChannel.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .doChannelController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'D.O. Channel Back Clamp',
                                      items: [],
                                      value:
                                          controller.doChannelBackClamp.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .doChannelBackClampController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'D.O. Fuse',
                                      items: [],
                                      value: controller.doFuse.value,
                                      onChanged:
                                          (val) =>
                                              controller.doFuseController.text =
                                                  val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Disc hardware',
                                      items: [],
                                      value: controller.discHardware.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .discHardwareController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Disc Insulator (Polymeric)',
                                      items: [],
                                      value:
                                          controller
                                              .discInsulatorPloymeric
                                              .value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .discInsulatorPolymericController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Disc Insulator (Porcelain)',
                                      items: [],
                                      value:
                                          controller
                                              .discInsulatorPorcelain
                                              .value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .discInsulatorPorcelainController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'DTR Base Channel',
                                      items: [],
                                      value: controller.dtrBaseChannel.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .dtrBaseChannelController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'DTR Spotting Angle',
                                      items: [],
                                      value: controller.dtrSpottingAngle.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .dtrSpottingAngleController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () =>
                                        controller
                                                .showFieldsBelowConductor
                                                .value
                                            ? Obx(() {
                                              if (controller
                                                  .showOptionalField
                                                  .value) {
                                                return CustomDropdownRow(
                                                  title:
                                                      'DTR Spotting Angle with Clamp',
                                                  items: [],
                                                  value:
                                                      controller
                                                          .dtrSpottingAngleWithClamp
                                                          .value,
                                                  onChanged:
                                                      (val) =>
                                                          controller
                                                              .dtrSpottingAngleWithClamp
                                                              .value = val ??
                                                              '',
                                                  isEditable: true,
                                                  showDropdown: true,
                                                );
                                              } else {
                                                return const SizedBox.shrink();
                                              }
                                            })
                                            : const SizedBox.shrink(),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'DVC Conductor',
                                      items: [],
                                      value: controller.dvcConductor.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .dvcConductorController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Earthing Conductor',
                                      items: [],
                                      value: controller.earthingConductor.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .earthingConductorController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Elbow',
                                      items: [],
                                      value: controller.elbow.value,
                                      onChanged:
                                          (val) =>
                                              controller.elbowController.text =
                                                  val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Eye-Bolt',
                                      items: [],
                                      value: controller.eyeBolt.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .eyeBoltController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'GI Pin',
                                      items: [],
                                      value: controller.giPin.value,
                                      onChanged:
                                          (val) =>
                                              controller.giPinController.text =
                                                  val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'GI Pipe',
                                      items: [],
                                      value: controller.giPipe.value,
                                      onChanged:
                                          (val) =>
                                              controller.giPipeController.text =
                                                  val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Greeper',
                                      items: [],
                                      value: controller.greeper.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .greeperController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Guy Insulator',
                                      items: [],
                                      value: controller.guyInsulator.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .guyInsulatorController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'I-Huck Clamp',
                                      items: [],
                                      value: controller.iHuckClamp.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .iHuckClampController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Lighting arrestor',
                                      items: [],
                                      value: controller.lightingArrestor.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .lightingArrestorController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Pin Insulator (polymeric)',
                                      items: [],
                                      value:
                                          controller
                                              .pinInsulatorPolymeric
                                              .value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .pinInsulatorPolymericController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Pin Insulator (Porcelain)',
                                      items: [],
                                      value:
                                          controller
                                              .pinInsulatorPorcelain
                                              .value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .pinInsulatorPorcelainController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Pole Earthing',
                                      items: [],
                                      value: controller.poleEarthing.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .poleEarthingController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Side Clamp',
                                      items: [],
                                      value: controller.sideClamp.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .sideClampController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Spotting Angle',
                                      items: [],
                                      value: controller.spottingAngle.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .spottingAngleController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Spotting Channel',
                                      items: [],
                                      value: controller.spottingChannel.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .spottingChannelController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Stay Clamp',
                                      items: [],
                                      value: controller.stayClamp.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .stayClampController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Stay Insulator',
                                      items: [],
                                      value: controller.stayInsulator.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .stayInsulatorController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Stay Road',
                                      items: [],
                                      value: controller.stayRoad.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .stayRoadController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Stay Wire 7/12',
                                      items: [],
                                      value: controller.stayWire.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .stayWire712Controller
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Suspension assembly clamp',
                                      items: [],
                                      value:
                                          controller
                                              .suspensionAssemblyClamp
                                              .value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .suspensionAssemblyClampController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Top Channel',
                                      items: [],
                                      value: controller.topChannel.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .topChannelController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Top Clamp',
                                      items: [],
                                      value: controller.topClamp.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .topClampController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Turn Buckle',
                                      items: [],
                                      value: controller.turnBuckle.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .turnBuckleController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'V Cross arm',
                                      items: [],
                                      value: controller.vCrossArm.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .vCrossArmController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'V Cross Back clamp',
                                      items: [],
                                      value: controller.vCrossBackClamp.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .vCrossBackClampController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'X-Bressing',
                                      items: [],
                                      value: controller.xBressing.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .xBressingController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                  Obx(
                                    () => CustomDropdownRow(
                                      title: 'Earthing Coil',
                                      items: [],
                                      value: controller.earthingCoil.value,
                                      onChanged:
                                          (val) =>
                                              controller
                                                  .earthingCoilController
                                                  .text = val ?? '',
                                      isEditable: true,
                                      showDropdown: true,
                                    ),
                                  ),
                                ],
                              )
                              : const SizedBox.shrink(),
                    ),

                    2.5.ph,
                    // Button to go to Next Screen
                    Center(
                      child: SizedBox(
                        width: 150,
                        child: CustomButton(
                          onTap: controller.validateAndProceed,
                          text: 'Next',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
