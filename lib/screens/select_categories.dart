import 'package:ems/common/constants/size.dart';
import 'package:ems/common/widgets/custom_button.dart';
import 'package:ems/routes/router.dart';
import 'package:ems/screens/Bottom_Nav_Bar/nav_screen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SelectCategories extends StatelessWidget {
  const SelectCategories({super.key});

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidght = MediaQuery.of(context).size.width;

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Image.asset(
            "assets/png/login_page.png",
            fit: BoxFit.fill,
            height: double.infinity,
            width: double.infinity,
          ),

          Positioned(
            top: screenHeight * 0.5,
            left: 0,
            right: 0,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                //mainAxisAlignment: MainAxisAlignment.center,
                // crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  3.5.ph,
                  Text(
                    "SELECT AN OPTION",
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  3.ph,
                  SizedBox(
                    width: screenWidght * .5,
                    child: CustomButton(onTap: () { 
                     Get.to(()=>NavScreen());
                    }, text: "Electricity"),
                  ),
                  3.ph,
                  SizedBox(
                    width: screenWidght * .5,
                    child: CustomButton(
                      onTap: () {
                        Navigator.pushNamed(context, gasScreen);
                      },
                      text: "Gas",
                    ),
                  ),
                  3.ph,
                  SizedBox(
                    width: screenWidght * .5,
                    child: CustomButton(
                      onTap: () {
                        Navigator.pushNamed(context, fibreOpticsScreen);
                      },
                      text: "Fibre Optics",
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
