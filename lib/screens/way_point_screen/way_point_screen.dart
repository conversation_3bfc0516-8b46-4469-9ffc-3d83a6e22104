import 'package:ems/common/constants/size.dart';
import 'package:ems/common/widgets/custom_app_bar.dart';
import 'package:ems/common/widgets/custom_button.dart';
import 'package:ems/common/widgets/custom_circularprogressindicator.dart';
import 'package:ems/common/widgets/custom_textformfield.dart';
import 'package:ems/screens/way_point_screen/way_point_screen_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class WayPointScreen extends StatelessWidget {
  WayPointScreen({super.key});

  final WayPointController controller = Get.put(WayPointController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "",
        onPressed: Get.back,
        backgroundColor: Colors.transparent,
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: SingleChildScrollView(
              child: Column(
                children: [

                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      "Create Waypoint",
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF4A3C2F),
                      ),
                    ),
                  ),
                  4.25.ph,
                  CustomTextformfield(
                    controller: controller.nameController,
                    hintText: "Name",
                    keyboardType: TextInputType.name,
                  ),
                  2.ph,
                  CustomTextformfield(
                    controller: controller.descriptionController,
                    hintText: "Description",
                    keyboardType: TextInputType.text,
                  ),
                  2.ph,
                  Obx(() => controller.showDistanceField.value
                    ? Column(
                        children: [
                          Obx(() => CustomTextformfield(
                            controller: controller.distanceController,
                            hintText: "Distance From Previous Waypoint (km)",
                            keyboardType: TextInputType.number,
                            readOnly: false,
                            suffixIcon: controller.isCalculatingDistance.value
                                ? Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: CustomCircularProgressIndicator(size: 10),
                                  )
                                : null,
                          )),
                          2.ph,
                        ],
                      )
                    : const SizedBox.shrink(),
                  ),
                  Obx(
                    () => CustomTextformfield(
                      controller: controller.latitudeController,
                      hintText: "Latitude",
                      keyboardType: TextInputType.number,
                      readOnly: true,
                      suffixIcon:
                          controller.isFetchingLocation.value
                              ? Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: CustomCircularProgressIndicator(size: 10,),
                              )
                              : null,
                    ),
                  ),
                  2.ph,
                  Obx(
                    () => CustomTextformfield(
                      controller: controller.longitudeController,
                      hintText: "Longitude",
                      keyboardType: TextInputType.number,
                      readOnly: true,
                      suffixIcon:
                          controller.isFetchingLocation.value
                              ? Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: CustomCircularProgressIndicator(size: 10,),
                              )
                              : null,
                    ),
                  ),
                  2.ph,
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.black),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Obx(
                        () => DropdownButton<String>(
                          value: controller.routeType.value,
                          isExpanded: true,
                          underline: const SizedBox(),
                          hint: const Text(
                            "Route Type",
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.normal,
                              color: Colors.black,
                            ),
                          ),
                          style: const TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w400,
                            color: Colors.black,
                          ),
                          items: controller.routeTypeOptions.map((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(
                                value,
                                style: const TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.black,
                                ),
                              ),
                            );
                          }).toList(),
                          onChanged: (newValue) {
                            if (newValue != null) {
                              controller.routeType.value = newValue;
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                  2.ph,
                  CustomTextformfield(
                    controller: controller.routeStartingPointController,
                    hintText: "Route Starting Point",
                    keyboardType: TextInputType.text,
                  ),
                  2.ph,
                  CustomTextformfield(
                    controller: controller.routeEndingPointController,
                    hintText: "Route Ending Point",
                    keyboardType: TextInputType.text,
                  ),
                  2.ph,
                  4.25.ph,
                  Row(
                    children: [
                      Expanded(
                        child: CustomButton(
                          text: "Cancel",
                          onTap: () => Get.back(),
                          color: Colors.white,
                          textColor: Colors.black,
                        ),
                      ),
                      2.pw,
                      Expanded(
                        child: CustomButton(
                          text: "Next",
                          onTap: controller.saveWayPoint,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
