import 'package:dio/dio.dart';
import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/common/constants/general.dart';
import 'package:ems/common/utils/distance_calculator.dart';
import 'package:ems/common/utils/shared_preferences.dart';
import 'package:ems/common/utils/upload_data_controller.dart';
import 'package:ems/screens/Bottom_Nav_Bar/nav_controller.dart';
import 'package:ems/screens/gps_details_screen/gps_details_screen.dart';
import 'package:ems/screens/home/<USER>';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:location/location.dart';

/// WayPointController manages the waypoint creation and data collection functionality
///
/// This controller is responsible for:
/// - Managing waypoint form fields (name, description, coordinates, distance)
/// - Handling device location services and permissions
/// - Fetching and displaying the current device location
/// - Calculating distances between waypoints
/// - Validating waypoint data before submission
/// - Storing waypoint information for further processing
/// - Coordinating with other controllers for data sharing
///
/// It uses GetX for reactive state management and the Location package
/// for accessing device location services.
class WayPointController extends GetxController {
  final nameController = TextEditingController();
  final descriptionController = TextEditingController();
  final distanceController = TextEditingController();
  final latitudeController = TextEditingController();
  final longitudeController = TextEditingController();
  final routeStartingPointController = TextEditingController();
  final routeEndingPointController = TextEditingController();

  final RxString routeType = 'Existing'.obs;
  final List<String> routeTypeOptions = ['Existing', 'New'];

  final Location location = Location();
  final isFetchingLocation = false.obs;
  final isCalculatingDistance = false.obs;

  final Rx<Map<String, double>?> startCoordinates = Rx<Map<String, double>?>(
    null,
  );
  final RxBool showDistanceField = false.obs;

  final Dio dio = Dio();

  /// Initializes the controller when it's first created
  ///
  /// This method:
  /// 1. Calls the parent class's onInit method
  /// 2. Ensures the UploadDataController is available as a permanent dependency
  /// 3. Fetches the starting coordinates from the NavController
  /// 4. Retrieves the current device location
  ///
  /// This initialization sequence ensures that all necessary data is available
  /// when the waypoint screen is displayed to the user.
  @override
  void onInit() {
    super.onInit();
    Get.put(UploadDataController(), permanent: true);
    fetchStartCoords();
    fetchCurrentLocation();
    fetchRouteInformation();
  }

  /// Cleans up resources when the controller is being disposed
  ///
  /// This method:
  /// 1. Disposes all TextEditingController instances to prevent memory leaks
  /// 2. Calls the parent class's onClose method
  ///
  /// Proper cleanup is essential to prevent memory leaks and ensure
  /// the application runs efficiently, especially when navigating
  /// between screens frequently.
  @override
  void onClose() {
    nameController.dispose();
    descriptionController.dispose();
    distanceController.dispose();
    latitudeController.dispose();
    longitudeController.dispose();
    routeStartingPointController.dispose();
    routeEndingPointController.dispose();
    super.onClose();
  }



  /// Calculates the distance between two geographic coordinates
  ///
  /// This method:
  /// 1. Sets the isCalculatingDistance flag to true to indicate loading state
  /// 2. Uses the DistanceCalculator utility to compute the distance
  /// 3. Handles any errors that might occur during calculation
  /// 4. Sets the isCalculatingDistance flag to false when complete
  ///
  /// The distance is calculated using Google's services for accurate
  /// road distance measurement rather than simple straight-line distance.
  ///
  /// @param startLat The latitude of the starting point
  /// @param startLng The longitude of the starting point
  /// @param endLat The latitude of the ending point
  /// @param endLng The longitude of the ending point
  /// @return The calculated distance in meters, or 0.0 if calculation fails
  Future<double> calculateDistance(
    double startLat,
    double startLng,
    double endLat,
    double endLng,
  ) async {
    try {
      isCalculatingDistance.value = true;
      return await DistanceCalculator.calculateDistance(
        startLat,
        startLng,
        endLat,
        endLng,
      );
    } catch (e) {
      return 0.0;
    } finally {
      isCalculatingDistance.value = false;
    }
  }

  /// Fetches the current device location with proper error handling
  ///
  /// This comprehensive method:
  /// 1. Sets the isFetchingLocation flag to true to indicate loading state
  /// 2. Checks if location services are enabled and requests them if not
  /// 3. Verifies location permissions and requests them if needed
  /// 4. Retrieves the current device location
  /// 5. Updates the latitude and longitude form fields
  /// 6. If starting coordinates exist:
  ///    a. Shows the distance field
  ///    b. Calculates the distance from the starting point to current location
  ///    c. Updates the distance field with the calculated value
  /// 7. Sets the isFetchingLocation flag to false when complete
  ///
  /// The method provides user feedback through snackbars and handles
  /// various error scenarios gracefully.
  void fetchCurrentLocation() async {
    isFetchingLocation.value = true;

    bool serviceEnabled;
    PermissionStatus permissionGranted;

    serviceEnabled = await location.serviceEnabled();
    if (!serviceEnabled) {
      isFetchingLocation.value = false;

      Get.snackbar(
        "Location Required",
        "Please enable location services.",
        snackPosition: SnackPosition.BOTTOM,
        mainButton: TextButton(
          onPressed: () async {
            bool opened = await location.requestService();
            if (opened) {
              fetchCurrentLocation();
            }
          },
          child: const Text("Open Settings"),
        ),
      );
      return;
    }

    permissionGranted = await location.hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await location.requestPermission();
      if (permissionGranted != PermissionStatus.granted) {
        isFetchingLocation.value = false;
        return;
      }
    }

    final locData = await location.getLocation();
    latitudeController.text = locData.latitude?.toString() ?? '';
    longitudeController.text = locData.longitude?.toString() ?? '';

    if (startCoordinates.value != null) {
      final startLat = startCoordinates.value!['latitude'] ?? 0.0;
      final startLng = startCoordinates.value!['longitude'] ?? 0.0;

      if (startLat != 0.0 || startLng != 0.0) {
        final currentLat = locData.latitude ?? 0.0;
        final currentLng = locData.longitude ?? 0.0;

        showDistanceField.value = true;
        distanceController.text = "Calculating...";

        try {
          final distance = await calculateDistance(
            startLat,
            startLng,
            currentLat,
            currentLng,
          );
          distanceController.text = distance.toStringAsFixed(0);
        } catch (e) {
          distanceController.text = "Error calculating";
        }
      } else {
        showDistanceField.value = false;
        distanceController.clear();
      }
    } else {
      showDistanceField.value = false;
      distanceController.clear();
    }

    isFetchingLocation.value = false;
  }

  /// Validates and saves the waypoint data, then navigates to the next screen
  ///
  /// This method:
  /// 1. Validates that all required fields are filled:
  ///    - Name
  ///    - Description
  ///    - Latitude
  ///    - Longitude
  ///    - Distance (if applicable)
  /// 2. If validation passes:
  ///    a. Transfers the waypoint data to the UploadDataController
  ///    b. Shows a success message
  ///    c. Navigates to the GPS Details screen
  ///    d. Clears all form fields
  /// 3. If validation fails, shows an error message
  ///
  /// This ensures that only complete and valid waypoint data is saved
  /// and that the user receives appropriate feedback.
  void saveWayPoint() {
    bool isValid =
        nameController.text.isNotEmpty &&
        descriptionController.text.isNotEmpty &&
        latitudeController.text.isNotEmpty &&
        longitudeController.text.isNotEmpty &&
        routeStartingPointController.text.isNotEmpty &&
        routeEndingPointController.text.isNotEmpty;

    if (showDistanceField.value) {
      isValid = isValid && distanceController.text.isNotEmpty;
    }

    if (isValid) {
      final uploadData = Get.find<UploadDataController>();
      uploadData.waypointName.value = nameController.text;
      uploadData.waypointDescription.value = descriptionController.text;
      uploadData.waypointDistance.value = distanceController.text;
      uploadData.waypointLatitude.value = latitudeController.text;
      uploadData.waypointLongitude.value = longitudeController.text;
      uploadData.routeType.value = routeType.value;
      uploadData.routeStartingPoint.value = routeStartingPointController.text;
      uploadData.routeEndingPoint.value = routeEndingPointController.text;

      Get.snackbar(
        "Success",
        "Waypoint saved successfully!",
        backgroundColor: AppColors.primaryOrange,
        snackPosition: SnackPosition.BOTTOM,
      );

      Get.to(() => GpsDetailsScreen());

      nameController.clear();
      descriptionController.clear();
      distanceController.clear();
      latitudeController.clear();
      longitudeController.clear();
      routeStartingPointController.clear();
      routeEndingPointController.clear();
      routeType.value = 'Existing';
    } else {
      Get.snackbar(
        "Error",
        "Please fill in all required fields.",
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Retrieves the starting coordinates from the NavController
  ///
  /// This method:
  /// 1. Gets a reference to the NavController
  /// 2. Retrieves the starting coordinates from it
  /// 3. Validates the coordinates to ensure they are usable:
  ///    - If coordinates are null, hides the distance field
  ///    - If coordinates are (0.0, 0.0), treats them as null and hides the distance field
  /// 4. Handles any errors by resetting coordinates and hiding the distance field
  ///
  /// This method is essential for determining whether to show the distance field
  /// and for calculating the distance from the starting point to the current location.
  void fetchStartCoords() async {
    try {
      final navController = Get.find<NavController>();
      startCoordinates.value = navController.startCoordinates.value;

      if (startCoordinates.value != null) {
        final startLat = startCoordinates.value!['latitude'] ?? 0.0;
        final startLng = startCoordinates.value!['longitude'] ?? 0.0;

        if (startLat == 0.0 && startLng == 0.0) {
          startCoordinates.value = null;
          showDistanceField.value = false;
        }
      } else {
        showDistanceField.value = false;
      }
    } catch (e) {
      startCoordinates.value = null;
      showDistanceField.value = false;
    }
  }

  /// Fetches route information from the API for waypoints with isStart=true
  ///
  /// This method:
  /// 1. Gets the authentication token and project ID
  /// 2. Makes an API request to fetch waypoints for the current project
  /// 3. Filters the response to find waypoints where isStart=true
  /// 4. Extracts routeType, routeStartingPoint, and routeEndingPoint
  /// 5. Updates the controller's fields with the retrieved information
  /// 6. Handles errors gracefully with appropriate logging
  ///
  /// This ensures that route information is pre-filled when creating new waypoints
  /// in an existing route, maintaining consistency across waypoints.
  Future<void> fetchRouteInformation() async {
    try {
      // Get the NavController to access the project ID
      final homeController = Get.find<HomeController>();
      final projectId = homeController.selectedProject.value?.projectId;

      // Get the authentication token
      final authToken = await TokenStorage.getToken();

      // Check if we have the necessary information
      if (projectId == null || authToken == null) {
        return;
      }

      // Prepare the API request
      final url = '$baseUrl/api/projects/$projectId/waypoints';
      final response = await dio.get(
        url,
        options: Options(headers: {'Authorization': 'Bearer $authToken'}),
      );

      // Process the response
      if (response.statusCode == 200 && response.data['success'] == true) {
        final List<dynamic> waypointArrays = response.data['waypoints'];

        // If there are no waypoints, return early
        if (waypointArrays.isEmpty) {
          return;
        }

        // Focus on the last array in the waypoints list (most recent route)
        final lastWaypointArray = waypointArrays.last;

        if (lastWaypointArray is List && lastWaypointArray.isNotEmpty) {
          // Check if the last waypoint in the array has isEnd=true
          final lastWaypoint = lastWaypointArray.last;
          if (lastWaypoint is Map<String, dynamic> && lastWaypoint['isEnd'] == true) {
            debugPrint("Most recent route is already completed (last waypoint has isEnd=true)");
            debugPrint("Not autofilling route information, letting user fill manually");
            return; // Route is completed, don't autofill
          }

          // Find the waypoint with isStart=true in this array to get route information
          for (final waypoint in lastWaypointArray) {
            if (waypoint is Map<String, dynamic> && waypoint['isStart'] == true) {
              // Found a waypoint with isStart=true, extract the route information
              final String routeTypeValue = waypoint['routeType'] ?? 'Existing';
              final String routeStartingPointValue = waypoint['routeStartingPoint'] ?? '';
              final String routeEndingPointValue = waypoint['routeEndingPoint'] ?? '';

              // Update the controller's fields
              routeType.value = routeTypeValue;
              routeStartingPointController.text = routeStartingPointValue;
              routeEndingPointController.text = routeEndingPointValue;

              return;
            }
          }
        } else {
          debugPrint("Last waypoint array is empty or not properly formatted");
        }
      } else {
        debugPrint("Failed to fetch waypoints: ${response.statusCode}");
        debugPrint("Response: ${response.data}");
      }
    } catch (e) {
      debugPrint("Error fetching route information: $e");
    }
  }
}
