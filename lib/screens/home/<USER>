import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/common/constants/size.dart';
import 'package:ems/common/widgets/custom_app_bar.dart';
import 'package:ems/common/widgets/custom_circularprogressindicator.dart';
import 'package:ems/models/project_model.dart';
import 'package:ems/screens/home/<USER>';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomeScreen extends GetView<HomeController> {
  final Function(Projects) onProjectSelected;
  const HomeScreen({super.key,required this.onProjectSelected});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(
      init: HomeController(),
      builder: (_) {
        if (controller.isLoading.value) {
          return const Scaffold(
            backgroundColor: AppColors.darkBlackandBrown,
            body: Center(child: CustomCircularProgressIndicator()),
          );
        }

        final appBar = CustomAppBar(
          title: "Project List",
          showBackButton: false,
          backgroundColor: AppColors.darkBlackandBrown,
          textColor: AppColors.white,
        );

        if (controller.hasNoProjects.value || controller.projects.isEmpty) {
          return Scaffold(
            backgroundColor: AppColors.darkBlackandBrown,
            appBar: appBar,
            body: RefreshIndicator(
              onRefresh: controller.getProjects,
              child: ListView(
                physics: const AlwaysScrollableScrollPhysics(),
                children: [
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.8,
                    child: const Center(
                      child: Text(
                        'No project is assigned',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        final projectData = controller.projects[0];
        return Scaffold(
          backgroundColor: AppColors.darkBlackandBrown,
          appBar: appBar,
          body: RefreshIndicator(
            onRefresh: controller.getProjects,
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                Text(
                  'Total Projects: ${projectData.count ?? 0}',
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.normal,
                    color: AppColors.white,
                  ),
                ),
                3.ph,
                if (projectData.projects != null)
                  ...projectData.projects!.map(
                    (project) => InkWell(
                      borderRadius: BorderRadius.circular(6),
                      onTap: () {
                        controller.selectProject(project);
                        onProjectSelected(project);
                      },
                      child: ProjectCard(
                        project: project,
                        isSelected:
                            controller.selectedProject.value?.projectId ==
                            project.projectId,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class ProjectCard extends StatelessWidget {
  final Projects project;
  final bool isSelected;

  const ProjectCard({
    super.key,
    required this.project,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color:
          isSelected
              ? AppColors.primaryOrange.withAlpha((0.3 * 255).round())
              : Color(0xFF373737),

      child: Padding(
        padding: const EdgeInsets.all(25),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Project ID : ${project.projectId ?? ''}",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.normal,
                color: AppColors.white,
              ),
            ),
            1.ph,
            Text(
              "Circle : ${project.circle ?? ''}",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.normal,
                color: AppColors.white,
              ),
            ),
            1.ph,
            Text(
              "Division : ${project.division ?? ''}",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.normal,
                color: AppColors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
