import 'package:dio/dio.dart';
import 'package:ems/common/utils/api_services.dart';
import 'package:ems/common/utils/network_client.dart';
import 'package:ems/common/utils/shared_preferences.dart';
import 'package:ems/models/project_model.dart';
import 'package:get/get.dart';

/// HomeController manages the home screen functionality and project selection
///
/// This controller is responsible for:
/// - Fetching and displaying the user's assigned projects
/// - Managing project selection state
/// - Handling loading states during API requests
/// - Providing project data to other parts of the application
/// - Managing the visibility of additional action buttons
///
/// It uses GetX for reactive state management and Dio for API requests.
class HomeController extends GetxController {
  List<ProjectModel> projects = [];

  final isLoading = true.obs;

  final hasNoProjects = false.obs;

  final selectedProject = Rx<Projects?>(null);

  final showAdditionalButtons = false.obs;
  final floatingSelected = false.obs;

  /// Toggles the visibility of additional action buttons
  ///
  /// This method:
  /// 1. Toggles the showAdditionalButtons flag between true and false
  /// 2. Toggles the floatingSelected flag to match the state
  ///
  /// These flags control the visibility and appearance of additional
  /// action buttons in the UI, providing users with access to secondary actions.
  void toggleAdditionalButtons() {
    showAdditionalButtons.toggle();
    floatingSelected.toggle();
  }

  /// Sets the currently selected project
  ///
  /// This method:
  /// 1. Updates the selectedProject reactive variable with the provided project
  ///
  /// The selected project is used throughout the application to determine
  /// which project's data to display and modify. This selection affects
  /// waypoint tracking, map displays, and other project-specific functionality.
  ///
  /// @param project The Projects object to set as the selected project
  void selectProject(Projects project) {
    selectedProject.value = project;
  }

  /// Clears the currently selected project
  ///
  /// This method:
  /// 1. Sets the selectedProject reactive variable to null
  ///
  /// This is used when the user needs to deselect the current project,
  /// such as when switching between projects or when logging out.
  /// It ensures that no project is selected when none should be.
  void clearSelection() {
    selectedProject.value = null;
  }

  /// Initializes the controller when it's first created
  ///
  /// This method:
  /// 1. Calls the parent class's onInit method
  /// 2. Triggers the getProjects method to fetch project data
  ///
  /// This ensures that project data is loaded as soon as the controller
  /// is initialized, making the data available when the UI is rendered.
  @override
  void onInit() {
    super.onInit();
    getProjects();
  }

  /// Fetches the user's assigned projects from the API
  ///
  /// This method:
  /// 1. Sets the loading state to true and resets the hasNoProjects flag
  /// 2. Retrieves the authentication token from storage
  /// 3. Makes an API request to get the user's assigned projects
  /// 4. Updates the projects list with the fetched data
  /// 5. Sets the hasNoProjects flag based on whether any projects were found
  /// 6. Handles various error scenarios with appropriate error messages
  /// 7. Updates the UI state regardless of success or failure
  /// 8. Sets the loading state to false when complete
  ///
  /// This is the primary method for loading project data and is called
  /// both during initialization and when the user manually refreshes the data.
  Future<void> getProjects() async {
    isLoading.value = true;
    hasNoProjects.value = false;

    String? authToken = await TokenStorage.getToken();
    try {
      if (authToken != null) {
        ProjectModel projectDetails = await getUserProjects(authToken);
        projects = [projectDetails];

        if (projectDetails.projects == null || projectDetails.projects!.isEmpty) {
          hasNoProjects.value = true;
        } else {
          hasNoProjects.value = false;
        }

        update();
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        hasNoProjects.value = true;
      } else {
        DioExceptions dioExceptions = DioExceptions.fromDioError(
          dioError: e,
          errorFrom: "USER PROFILE",
        );
        DioExceptions.showErrorMessage(message: dioExceptions.errorMessage());
      }
      update();
    } finally {
      isLoading.value = false;
    }
  }
}
