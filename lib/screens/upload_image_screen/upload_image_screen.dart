import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/common/constants/size.dart';
import 'package:ems/common/widgets/custom_app_bar.dart';
import 'package:ems/common/widgets/custom_button.dart';
import 'package:ems/common/widgets/dotted_border_widget.dart';
import 'package:ems/screens/upload_image_screen/upload_image_screen_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UploadImageScreen extends StatelessWidget {
  const UploadImageScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(UploadImageScreenController());

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: 'UPLOAD IMAGE',
        showBackButton: true,
        backButtonColor: Colors.black,
        textColor: Colors.black,
        backgroundColor: Colors.transparent,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const Text(
                'Upload and manage your\nImages',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.black54,
                ),
              ),
              5.ph,
              Obx(() => DottedBorderWidget(
                onTap: controller.pickImages,
                onCameraTap: controller.takePhoto,
                isDisabled: controller.isMaxImagesReached,
              )),
              2.5.ph,
              Expanded(
                child: Obx(() => ListView.builder(
                  itemCount: controller.uploadedImages.length,
                  itemBuilder: (context, index) {
                    final file = controller.uploadedImages[index];
                    final fileName = file.path.split('/').last;
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 5.0),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                        decoration: BoxDecoration(
                          color: AppColors.lightGrey,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.image, color: AppColors.primaryOrange),
                            1.pw,
                            Expanded(child: Text(fileName, overflow: TextOverflow.ellipsis)),
                            IconButton(
                              icon: const Icon(Icons.remove_red_eye, color: Colors.blue),
                              onPressed: () => controller.viewImage(file),
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () => controller.removeImage(file),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                )),
              ),
              CustomButton(
                onTap: controller.saveData,
                text: 'Save',
              ),
            ],
          ),
        ),
      ),
    );
  }
}