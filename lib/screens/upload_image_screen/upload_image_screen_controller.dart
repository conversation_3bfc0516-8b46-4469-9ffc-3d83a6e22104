import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart' as dio;
import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/common/constants/general.dart';
import 'package:ems/common/utils/shared_preferences.dart';
import 'package:ems/common/utils/upload_data_controller.dart';
import 'package:ems/screens/Bottom_Nav_Bar/nav_controller.dart';
import 'package:ems/screens/Bottom_Nav_Bar/nav_screen.dart';
import 'package:ems/screens/home/<USER>';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';

/// UploadImageScreenController manages the image upload and data submission functionality
///
/// This controller is responsible for:
/// - Managing image selection and validation
/// - Enforcing image upload limits
/// - Handling image preview and removal
/// - Preparing and formatting waypoint data for submission
/// - Submitting collected data to the API
/// - Handling success and error states during submission
/// - Converting data formats for API compatibility
///
/// It uses GetX for reactive state management, Dio for API requests,
/// and ImagePicker for image selection from the gallery or camera.
class UploadImageScreenController extends GetxController {
  RxList<File> uploadedImages = <File>[].obs;

  final int maxImages = 1;

  /// Determines if the maximum number of allowed images has been reached
  ///
  /// This computed property:
  /// 1. Compares the current number of uploaded images to the maximum allowed
  /// 2. Returns true if the limit has been reached, false otherwise
  ///
  /// Used to enforce the image upload limit throughout the controller.
  bool get isMaxImagesReached => uploadedImages.length >= maxImages;

  /// Opens the gallery to allow the user to select an image
  ///
  /// This method:
  /// 1. Checks if the maximum number of images has been reached
  /// 2. Shows a warning snackbar if the limit is reached
  /// 3. Opens the system gallery for image selection
  /// 4. Processes the selected file if one was chosen
  /// 5. Adds the selected image to the uploadedImages list
  ///
  /// The method enforces single image selection and only allows image file types.
  void pickImages() async {
    if (isMaxImagesReached) {
      Get.snackbar(
        "Limit Reached",
        "You can only upload 1 image",
        backgroundColor: AppColors.primaryOrange,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 80, // Reduce image quality to save space
    );

    if (image != null) {
      File file = File(image.path);
      uploadedImages.add(file);
    }
  }

  /// Opens the camera to allow the user to take a photo
  ///
  /// This method:
  /// 1. Checks if the maximum number of images has been reached
  /// 2. Shows a warning snackbar if the limit is reached
  /// 3. Opens the device camera
  /// 4. Processes the captured image if one was taken
  /// 5. Adds the captured image to the uploadedImages list
  ///
  /// The method enforces the image upload limit and handles camera permissions.
  void takePhoto() async {
    if (isMaxImagesReached) {
      Get.snackbar(
        "Limit Reached",
        "You can only upload 1 image",
        backgroundColor: AppColors.primaryOrange,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    final ImagePicker picker = ImagePicker();
    final XFile? photo = await picker.pickImage(
      source: ImageSource.camera,
      imageQuality: 80,
    );

    if (photo != null) {
      File file = File(photo.path);
      uploadedImages.add(file);
    }
  }

  /// Removes a specific image from the uploaded images list
  ///
  /// This method:
  /// 1. Takes a File object as a parameter
  /// 2. Removes that file from the uploadedImages list if it exists
  ///
  /// This allows users to delete images they no longer want to upload.
  ///
  /// @param file The File object to remove from the uploaded images
  void removeImage(File file) {
    uploadedImages.remove(file);
  }

  /// Displays a preview dialog of the selected image
  ///
  /// This method:
  /// 1. Takes a File object as a parameter
  /// 2. Extracts the filename from the file path
  /// 3. Creates and displays a dialog with the image and its filename
  ///
  /// This allows users to preview images before uploading them.
  ///
  /// @param file The File object to display in the preview dialog
  void viewImage(File file) {
    Get.defaultDialog(
      title: file.path.split('/').last,
      content: Image.file(file),
    );
  }

  /// Uploads all collected waypoint data and images to the server
  ///
  /// This comprehensive method:
  /// 1. Retrieves data from various controllers (UploadData, Home, Nav)
  /// 2. Prepares the authentication token and project ID
  /// 3. Parses and formats GPS coordinates
  /// 4. Constructs detailed data maps for GPS and pole details
  /// 5. Converts field names to camelCase for API compatibility
  /// 6. Creates a multipart form data request with all collected information
  /// 7. Attaches the selected image if available
  /// 8. Submits the data to the API
  /// 9. Handles success and error responses
  /// 10. Cleans up and navigates to the appropriate screen on completion
  ///
  /// This is the main method responsible for submitting all collected data
  /// to the server and completing the waypoint tracking process.
  Future<void> uploadWaypointData() async {
    final uploadData = Get.find<UploadDataController>();
    final homeController = Get.find<HomeController>();
    final navController = Get.find<NavController>();
    final dioClient = dio.Dio();
    final String? authToken = await TokenStorage.getToken();
    final String? projectId = homeController.selectedProject.value?.projectId;

    final gpsStartPointsCoords = uploadData.gpsCoordinatesStartPoint.value
        .split(',');
    double parsedLat = 0.0;
    double parsedLong = 0.0;

    if (gpsStartPointsCoords.length == 2) {
      parsedLat = double.tryParse(gpsStartPointsCoords[0].trim()) ?? 0.0;
      parsedLong = double.tryParse(gpsStartPointsCoords[1].trim()) ?? 0.0;
    }

    try {
      final url = '$baseUrl/api/projects/$projectId/waypoints';

      // Check if conductor is "Not Applicable"
      final isConductorNotApplicable = uploadData.conductor.value == 'Not Applicable';

      // Create GPS materials map based on conductor value
      final Map<String, dynamic> gpsMaterials;
      if (isConductorNotApplicable) {
        // If conductor is "Not Applicable", don't include fields below conductor
        gpsMaterials = {};
      } else {
        // If conductor is any other value, include all fields
        gpsMaterials = uploadData.gpsMaterialQuantities.map(
          (key, value) =>
              MapEntry(_convertToCamelCase(key), int.tryParse(value) ?? 0),
        );
      }

      // Create Pole materials map based on conductor value
      final Map<String, dynamic> poleMaterials;
      if (isConductorNotApplicable) {
        // If conductor is "Not Applicable", include pole details
        poleMaterials = uploadData.poleMaterialQuantities.map(
          (key, value) =>
              MapEntry(_convertToCamelCase(key), int.tryParse(value) ?? 0),
        );
      } else {
        // If conductor is any other value, send empty pole details
        poleMaterials = {};
      }

      // Base GPS details that are always included
      final Map<String, dynamic> gpsDetailsMap = {
        "timeAndDate": uploadData.dateTime.value,
        "userId": uploadData.userId.value,
        "district": uploadData.district.value,
        "routeStartPoint": uploadData.routeStartPoint.value,
        "lengthInMeter": double.tryParse(uploadData.length.value) ?? 0,
        "startPointCoordinates": {
          "latitude": parsedLat,
          "longitude": parsedLong,
        },
        "currentWaypointCoordinates": {
          "latitude": double.tryParse(uploadData.waypointLatitude.value) ?? 0,
          "longitude": double.tryParse(uploadData.waypointLongitude.value) ?? 0,
        },
        "substationName": uploadData.substationName.value,
        "feederName": uploadData.feederName.value,
        "conductor": uploadData.conductor.value,
      };

      // Add additional fields based on conductor value
      if (!isConductorNotApplicable) {
        // If conductor is not "Not Applicable", include fields below conductor
        gpsDetailsMap.addAll({
          "cable": uploadData.cable.value,
          "transformerLocation": uploadData.transformerLocation.value,
          "transformerType": uploadData.transformerType.value,
          "transformerPole": uploadData.transformerPole.value,
          "transformerKV": uploadData.transformerKV.value,
          ...gpsMaterials,
        });
      }

      // Create pole details map based on conductor value
      final Map<String, dynamic> poleDetailsMap;
      if (isConductorNotApplicable) {
        // If conductor is "Not Applicable", include pole details
        poleDetailsMap = {
          "poleNo": int.tryParse(uploadData.poleNumber.value) ?? 0,
          "existingOrNewProposed": uploadData.existingOrNew.value,
          "poleDiscription": uploadData.poleDescription.value,
          "poleType": uploadData.poleType.value,
          "poleSizeInMeter": int.tryParse(uploadData.poleSize.value) ?? 0,
          "poleStructure": uploadData.poleStructure.value,
          ...poleMaterials,
        };
      } else {
        // If conductor is any other value, send empty pole details
        poleDetailsMap = {};
      }

      final formData = dio.FormData();

      formData.fields.addAll([
        MapEntry('name', uploadData.waypointName.value),
        MapEntry('description', uploadData.waypointDescription.value),
        MapEntry(
          'distanceFromPrevious',
          (double.tryParse(uploadData.waypointDistance.value) ?? 0).toString(),
        ),
        MapEntry(
          'latitude',
          (double.tryParse(uploadData.waypointLatitude.value) ?? 0).toString(),
        ),
        MapEntry(
          'longitude',
          (double.tryParse(uploadData.waypointLongitude.value) ?? 0).toString(),
        ),
        MapEntry('routeType', uploadData.routeType.value),
        MapEntry('routeStartingPoint', uploadData.routeStartingPoint.value),
        MapEntry('routeEndingPoint', uploadData.routeEndingPoint.value),
        MapEntry('isStart', navController.isStart.value.toString()),
        MapEntry('isEnd', navController.isEnd.value.toString()),
      ]);



      // Convert gpsDetails and poleDetails to strings and pass them as form-data
      formData.fields.add(
        MapEntry('gpsDetails', '[${jsonEncode(gpsDetailsMap)}]'),
      );

      // If conductor is "Not Applicable", include pole details
      // Otherwise, send an empty array for pole details
      if (isConductorNotApplicable) {
        formData.fields.add(
          MapEntry('poleDetails', '[${jsonEncode(poleDetailsMap)}]'),
        );
      } else {
        formData.fields.add(
          MapEntry('poleDetails', '[]'),
        );
      }

      if (uploadedImages.isNotEmpty) {
        final file = uploadedImages.first;
        final fileName = file.path.split('/').last;
        final mimeType = lookupMimeType(file.path);

        final mimeSplit =
            mimeType?.split('/') ?? ['image', 'jpeg'];

        formData.files.add(
          MapEntry(
            'image',
            await dio.MultipartFile.fromFile(
              file.path,
              filename: fileName,
              contentType: MediaType(mimeSplit[0], mimeSplit[1]),
            ),
          ),
        );
      } else {
        formData.fields.add(MapEntry('image', 'string'));
      }

      final response = await dioClient.post(
        url,
        data: formData,
        options: dio.Options(
          headers: {
            'Authorization': 'Bearer $authToken',
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        Get.delete<UploadDataController>();

        navController.isStart.value = false;
        navController.isEnd.value = false;

        await navController.fetchLatestWaypointStatus();

        Get.snackbar(
          "Success",
          "Data uploaded successfully!",
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );

        Get.offAll(() => NavScreen());
      } else {
        Get.snackbar(
          "Error",
          "Failed to upload data",
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        "Error",
        "Upload failed: $e",
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      navController.isStart.value = false;
      navController.isEnd.value = false;
    }
  }

  /// Converts snake_case strings to camelCase format
  ///
  /// This method:
  /// 1. Takes a string in snake_case format (e.g., "distribution_box")
  /// 2. Uses regex to find all occurrences of underscore followed by a character
  /// 3. Replaces each match with the uppercase version of the character
  /// 4. Returns the resulting camelCase string (e.g., "distributionBox")
  ///
  /// This is used to convert field names from the format used in the app
  /// to the format expected by the API.
  ///
  /// @param key The snake_case string to convert
  /// @return The converted camelCase string
  String _convertToCamelCase(String key) {
    return key.replaceAllMapped(
      RegExp(r'_(\w)'),
      (match) => match[1]!.toUpperCase(),
    );
  }

  /// Initiates the data saving process
  ///
  /// This method:
  /// 1. Calls the uploadWaypointData method to start the data submission process
  ///
  /// This is a convenience method that serves as the main entry point
  /// for the data saving process, typically called from the UI when
  /// the user taps the save button.
  void saveData() {
    uploadWaypointData();
  }
}
