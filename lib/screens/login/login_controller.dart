import 'package:dio/dio.dart';
import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/common/constants/general.dart';
import 'package:ems/common/utils/clear_all_controllers.dart';
import 'package:ems/common/utils/shared_preferences.dart';
import 'package:ems/screens/select_categories.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// LoginController handles the authentication logic for the application
///
/// This controller manages the login form state, user credentials validation,
/// API authentication requests, and navigation after successful login.
/// It uses GetX for state management and Dio for API requests.
class LoginController extends GetxController {
  final formKey = GlobalKey<FormState>();
  final employeeIdController = TextEditingController();
  final passwordController = TextEditingController();
  var dioClient = Dio();
  final RxBool _obscurePassword = true.obs;

  bool get obscurePassword => _obscurePassword.value;

  IconData get passwordIcon =>
      _obscurePassword.value ? Icons.visibility : Icons.visibility_off;

  /// Toggles the password visibility between visible and hidden states
  ///
  /// This function is called when the user taps on the password visibility toggle icon
  void togglePasswordVisibility() {
    _obscurePassword.value = !_obscurePassword.value;
  }

  /// Clears all text input controllers in the login form
  ///
  /// This method resets the employee ID and password fields to empty strings
  /// Used after successful login or when user wants to reset the form
  void clear() {
    clearControllers([employeeIdController, passwordController]);
  }

  /// Authenticates the user with the provided credentials
  ///
  /// This method:
  /// 1. Validates the login form
  /// 2. Makes an API request to authenticate the user
  /// 3. Stores the authentication token on successful login
  /// 4. Navigates to the SelectCategories screen
  /// 5. Handles various error scenarios with appropriate user feedback
  ///
  /// @param context The BuildContext used for showing snackbars and checking if widget is mounted
  Future<void> signIn(BuildContext context) async {
    // Validate form before proceeding
    if (!formKey.currentState!.validate()) {
      return;
    }

    final url = '$baseUrl/api/auth/login-employee';
    Map<String, dynamic> authData = {
      "empId": employeeIdController.text.trim(),
      "password": passwordController.text.trim(),
    };

    try {
      //String jsonData = jsonEncode(authData);
      final response = await dioClient.post(url, data: authData);
      if (response.statusCode == 200) {
        if (response.data is Map<String, dynamic> &&
            response.data.containsKey('token')) {
          final String token = response.data['token'] as String;

          await TokenStorage.saveToken(token);

          Get.offAll(() => SelectCategories());
          clear();
        }
      }
    } on DioException catch (e) {
      update();
      if (e.response != null && e.response?.statusCode == 401) {
        final errorData = e.response?.data;
        if (errorData is Map<String, dynamic> &&
            errorData['message'] == "Invalid Credentials") {
          if (context.mounted) {
            Get.snackbar(
              "Invalid Credentials",
              "Please check your employee ID and password.",
              backgroundColor: AppColors.red,
              colorText: AppColors.white,
              snackPosition: SnackPosition.BOTTOM,
            );
          }
        } else {
          if (context.mounted) {
            Get.snackbar(
              'Error',
              'Login Failed',
              colorText: Colors.white,
              backgroundColor: Colors.red,
              snackPosition: SnackPosition.BOTTOM,
            );
          }
        }
      } else {
        if (context.mounted) {
          Get.snackbar(
            'Unexpected Error',
            '${e.message}',
            colorText: Colors.white,
            backgroundColor: Colors.red,
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      }
    }
  }
}
