import 'package:ems/common/constants/size.dart';
import 'package:ems/common/utils/validators.dart';
import 'package:ems/common/widgets/custom_button.dart';
import 'package:ems/common/widgets/custom_textformfield.dart';
import 'package:ems/screens/login/login_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LoginScreen extends GetView<LoginController> {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final isKeyboardVisible = MediaQuery.of(context).viewInsets.bottom > 100;

    return GetBuilder<LoginController>(
      init: LoginController(),
      builder: (_) {
        return Scaffold(
          resizeToAvoidBottomInset: true,
          body: Stack(
            children: [
              // Fixed background image
              Positioned.fill(
                child: Image.asset(
                  "assets/png/login_page.png",
                  fit: BoxFit.fill,
                ),
              ),

              // Scrollable form
              SingleChildScrollView(
                child: <PERSON><PERSON><PERSON><PERSON>(
                  height: screenHeight,
                  width: screenWidth,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Form(
                        key: controller.formKey,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Column(
                            children: [
                              2.ph,
                              if (!isKeyboardVisible) ...[
                                const Text(
                                  "SIGN IN TO YOUR",
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    shadows: [
                                      Shadow(
                                        offset: Offset(0, 1),
                                        blurRadius: 2,
                                        color: Colors.black45,
                                      ),
                                    ],
                                  ),
                                ),
                                const Text(
                                  "ACCOUNT",
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    shadows: [
                                      Shadow(
                                        offset: Offset(0, 1),
                                        blurRadius: 2,
                                        color: Colors.black45,
                                      ),
                                    ],
                                  ),
                                ),
                                3.ph,
                              ],

                              const Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  "EMPLOYEE ID",
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              1.ph,
                              CustomTextformfield(
                                hintText: "Enter Your Employee ID",
                                controller: controller.employeeIdController,
                                keyboardType: TextInputType.emailAddress,
                                validator:
                                    (value) => Validators.validator(
                                      value,
                                      "Employee ID",
                                    ),
                              ),
                              1.ph,
                              const Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  "Password",
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              1.ph,
                              Obx(
                                () => CustomTextformfield(
                                  hintText: "Enter Your Password",
                                  controller: controller.passwordController,
                                  keyboardType: TextInputType.visiblePassword,
                                  isPassword: controller.obscurePassword,
                                  validator:
                                      (value) => Validators.validator(
                                        value,
                                        "Password",
                                      ),
                                  suffixIcon: IconButton(
                                    icon: Icon(controller.passwordIcon),
                                    onPressed:
                                        controller.togglePasswordVisibility,
                                  ),
                                ),
                              ),
                              2.ph,
                              SizedBox(
                                width: screenWidth * .5,
                                child: CustomButton(
                                  onTap: () => controller.signIn(context),
                                  text: "SIGN IN",
                                ),
                              ),
                              5.ph,
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
