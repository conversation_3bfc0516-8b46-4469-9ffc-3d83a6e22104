import 'package:dio/dio.dart';

import 'package:ems/common/utils/api_services.dart';
import 'package:ems/common/utils/network_client.dart';
import 'package:ems/common/utils/shared_preferences.dart';
import 'package:ems/models/profile_model.dart';
import 'package:get/get.dart';

/// ProfileController manages the user profile functionality
///
/// This controller is responsible for:
/// - Fetching and displaying the user's profile information
/// - Handling API requests related to user profile data
/// - Managing error handling for profile-related operations
/// - Providing profile data to the UI components
///
/// It uses GetX for state management and Dio for API requests.
class ProfileController extends GetxController {
  List<ProfileModel> profile = [];

  /// Initializes the controller when it's first created
  ///
  /// This method:
  /// 1. Calls the parent class's onInit method
  /// 2. Triggers the getProfile method to fetch user profile data
  ///
  /// This ensures that profile data is loaded as soon as the controller
  /// is initialized, making the data available when the UI is rendered.
  @override
  void onInit() {
    super.onInit();
    getProfile();
  }

  /// Fetches the user's profile information from the API
  ///
  /// This method:
  /// 1. Retrieves the authentication token from storage
  /// 2. Makes an API request to get the user's profile details
  /// 3. Updates the profile list with the fetched data
  /// 4. Triggers UI update to reflect the new data
  /// 5. Handles any API errors with appropriate error messages
  ///
  /// The profile data includes personal information, contact details,
  /// and other user-specific information that is displayed in the profile screen.
  Future<void> getProfile() async {
    String? authToken = await TokenStorage.getToken();
    try {
      if (authToken != null) {
        ProfileModel profileDetails = await getUserProfile(authToken);
        profile = [profileDetails];
        update();
      }
    } on DioException catch (e) {
      update();
      DioExceptions dioExceptions = DioExceptions.fromDioError(
        dioError: e,
        errorFrom: "USER PROFILE",
      );
      DioExceptions.showErrorMessage(message: dioExceptions.errorMessage());
    }
  }
}
