import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/common/constants/size.dart';
import 'package:ems/common/utils/shared_preferences.dart';
import 'package:ems/common/widgets/custom_button.dart';
import 'package:ems/common/widgets/custom_circularprogressindicator.dart';
import 'package:ems/screens/history_screen/history_screen.dart';
import 'package:ems/screens/login/login_screen.dart';
import 'package:ems/screens/profile/profile_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileScreen extends GetView<ProfileController> {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return GetBuilder<ProfileController>(
      init: ProfileController(),
      builder: (_) {
        if (controller.profile.isEmpty) {
          return const Scaffold(
            body: Center(child: CustomCircularProgressIndicator()),
          );
        }
        return Scaffold(
          resizeToAvoidBottomInset: false,
          body: Stack(
            children: [
              Image.asset(
                "assets/png/profile_background.png",
                fit: BoxFit.fill,
                height: double.infinity,
                width: double.infinity,
              ),

              Positioned(
                top: screenHeight * 0.25,
                left: 0,
                right: 0,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    children: [
                      _profileImage(),
                      2.ph,

                      Text(
                        controller.profile[0].name ?? "Ram",

                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        "Employee ID : ${controller.profile[0].empId}",
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      4.ph,
                      _actionButtons(context, screenHeight, screenWidth),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _profileImage() {
    return Container(
      height: 90,
      width: 90,
      decoration: BoxDecoration(
        border: Border.all(width: 3, color: AppColors.primaryOrange),
        color: AppColors.lightGrey,
        shape: BoxShape.circle,
      ),
      child: ClipOval(
        child: controller.profile[0].image != null
            ? Image.network(
                controller.profile[0].image!,
                fit: BoxFit.cover,
                width: 90,
                height: 90,
                errorBuilder: (context, error, stackTrace) {
                  return Image.asset(
                    "assets/png/usericons.png",
                    fit: BoxFit.cover,
                    width: 90,
                    height: 90,
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                      color: AppColors.primaryOrange,
                    ),
                  );
                },
              )
            : Image.asset(
                "assets/png/usericons.png",
                fit: BoxFit.cover,
                width: 90,
                height: 90,
              ),
      ),
    );
  }

  Widget _actionButtons(
    BuildContext context,
    double screenHeight,
    double screenWidth,
  ) {
    return Container(
      height: screenHeight * .15,
      width: screenWidth,
      decoration: BoxDecoration(
        color: Color(0xFF534439),
        borderRadius: BorderRadius.circular(24),
      ),
      child: Column(
        children: [
          _actionButton(
            context,
            "assets/png/history_icon.png",
            "History",
            () => Get.to(() => HistoryScreen()),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: Container(
              height: 1,
              color: Color.fromRGBO(255, 255, 255, 0.08),
              width: double.infinity,
            ),
          ),
          _actionButton(
            context,
            "assets/png/logout_icon.png",
            "Logout",
            () => _showLogoutDialog(context, screenHeight, screenWidth),
          ),
        ],
      ),
    );
  }

  Widget _actionButton(
    BuildContext context,
    String iconPath,
    String text,
    VoidCallback onTap,
  ) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(iconPath),
            2.pw,
            Text(
              text,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppColors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog(
    BuildContext context,
    double screenHeight,
    double screenWidth,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          backgroundColor: Color(0xFF252525),
          insetPadding: EdgeInsets.symmetric(horizontal: 20),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  "DO YOU WANT TO LOGOUT?",
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                3.ph,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    SizedBox(
                      width: screenWidth * .25,
                      child: CustomButton(
                        onTap: () async {
                          await TokenStorage.deleteToken();
                          Get.offAll(() => LoginScreen());
                        },
                        text: "Logout",
                      ),
                    ),
                    SizedBox(
                      width: screenWidth * .25,
                      child: CustomButton(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        color: Color(0xFF534439),
                        text: "Cancel",
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
