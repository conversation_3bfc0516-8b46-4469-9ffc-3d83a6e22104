import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/screens/home/<USER>';
import 'package:ems/screens/map_screen/map_screen.dart';
import 'package:ems/screens/profile/profile_screen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:stylish_bottom_bar/stylish_bottom_bar.dart';

import 'nav_controller.dart';

class NavScreen extends GetView<NavController> {
  const NavScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(NavController(), permanent: true);

    return GetBuilder(
      initState: (_) {},
      init: controller,
      builder: (_) {
        return Scaffold(
          extendBody: true,
          body: Stack(
            children: [
              PageView(
                controller: controller.pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  HomeScreen(
                    onProjectSelected: (project) {
                      controller.showProjectMap(project);
                      controller.fetchLatestWaypointStatus();
                    },
                  ),
                  const ProfileScreen(),
                ],
              ),
              if (controller.showMapScreen.value)
                IgnorePointer(
                  ignoring: controller.selectedIndex.value != 0,
                  child: MapScreen(
                    project: controller.selectedMapProject.value!,
                    onBack: controller.hideMapScreen,
                  ),
                ),
            ],
          ),
          bottomNavigationBar: Obx(() => _buildBottomNavigationBar()),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          floatingActionButton: Obx(() => _buildFloatingActionButton()),
        );
      },
    );
  }

  Widget _buildBottomNavigationBar() {
    return SizedBox(
      height: 65,
      child: StylishBottomBar(
        items: [
          _buildBottomBarItem("home_icon.png"),
          _buildBottomBarItem("profile_icon.png"),
        ],
        option: AnimatedBarOptions(iconStyle: IconStyle.simple),
        backgroundColor: AppColors.primaryOrange,
        hasNotch: true,
        notchStyle: NotchStyle.circle,
        fabLocation: StylishBarFabLocation.center,
        currentIndex: controller.selectedIndex.value,
        onTap: (index) {
          controller.changeTab(index);
          if (index != 0) {
            controller.hideMapScreen();
          }
        },
      ),
    );
  }

  BottomBarItem _buildBottomBarItem(String iconName) {
    return BottomBarItem(
      icon: Image.asset("assets/png/$iconName", color: AppColors.white),
      title: const Text(''),
      selectedIcon:
          controller.floatingSelected.value
              ? Image.asset("assets/png/$iconName", color: AppColors.white)
              : Container(
                height: 36,
                width: 36,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFFFC9B12), Color(0xFFB32C43)],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  shape: BoxShape.circle,
                ),
                child: Image.asset(
                  "assets/png/$iconName",
                  color: AppColors.white,
                ),
              ),
    );
  }

  Widget _buildFloatingActionButton() {
    final isProjectSelected =
        controller.homeController.selectedProject.value != null;
    return FloatingActionButton(
      heroTag: 'main_fab',
      elevation: 4,
      shape: const CircleBorder(),
      backgroundColor:
          isProjectSelected ? AppColors.primaryOrange : Colors.grey,
      onPressed: controller.toggleAdditionalButtons,
      child: Obx(
        () => AnimatedRotation(
          duration: const Duration(milliseconds: 300),
          turns: controller.showAdditionalButtons.value ? 0.125 : 0,
          child: Image.asset(
            "assets/png/plus_icon.png",
            color:
                controller.floatingSelected.value
                    ? AppColors.black
                    : AppColors.white,
          ),
        ),
      ),
    );
  }
}
