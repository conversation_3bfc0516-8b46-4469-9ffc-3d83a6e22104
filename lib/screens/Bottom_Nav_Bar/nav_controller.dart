import 'package:dio/dio.dart';
import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/common/constants/general.dart';
import 'package:ems/common/utils/shared_preferences.dart';
import 'package:ems/models/project_model.dart';
import 'package:ems/screens/home/<USER>';
import 'package:ems/screens/way_point_screen/way_point_screen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// NavController manages the bottom navigation bar and floating action buttons
///
/// This controller is responsible for:
/// - Managing navigation between different tabs in the application
/// - Handling the floating action button and its expanded state
/// - Controlling the overlay for additional buttons
/// - Managing waypoint tracking state (start, add waypoint, stop)
/// - Fetching and updating waypoint status from the API
/// - Showing and hiding the map screen
/// - Storing coordinates for waypoint tracking
///
/// It uses GetX for state management and reactive programming.
class NavController extends GetxController {

  // State variables
  final PageController pageController = PageController(initialPage: 0);
  final RxInt selectedIndex = 0.obs;
  final RxBool floatingSelected = false.obs;
  final RxBool showAdditionalButtons = false.obs;
  OverlayEntry? overlayEntry;
  final RxBool showMapScreen = false.obs;
  final Rxn<Projects> selectedMapProject = Rxn<Projects>();

  final RxBool isStart = false.obs;
  final RxBool isEnd = false.obs;

  final RxBool canStart = true.obs;
  final RxBool canAddWaypoint = true.obs;
  final RxBool canStop = true.obs;

  late final HomeController homeController;

  final Rx<Map<String, double>?> previousCoordinates =
      Rx<Map<String, double>?>(null);

  final Rx<Map<String, double>?> startCoordinates =
      Rx<Map<String, double>?>(null);

  /// Initializes the controller when it's first created
  ///
  /// This method:
  /// 1. Initializes the HomeController as a permanent dependency
  /// 2. Ensures the HomeController is available throughout the app lifecycle
  ///
  /// The permanent flag ensures the controller isn't garbage collected
  /// when the view is disposed, maintaining state across navigation.
  @override
  void onInit() {
    homeController = Get.put(HomeController(), permanent: true);
    super.onInit();
  }

  /// Cleans up resources when the controller is being disposed
  ///
  /// This method:
  /// 1. Removes any active overlay to prevent memory leaks
  /// 2. Disposes the PageController to free up resources
  ///
  /// Proper cleanup is essential to prevent memory leaks and ensure
  /// the application runs efficiently.
  @override
  void onClose() {
    removeOverlay();
    pageController.dispose();
    super.onClose();
  }

  /// Fetches the latest waypoint status from the API and updates button states
  ///
  /// This method:
  /// 1. Retrieves the authentication token and current project ID
  /// 2. Makes an API request to get the waypoints for the selected project
  /// 3. Analyzes the waypoint data to determine the current tracking state
  /// 4. Updates the UI state variables (canStart, canAddWaypoint, canStop)
  /// 5. Stores relevant coordinates for tracking continuation
  ///
  /// The method handles different scenarios:
  /// - Empty waypoints: New project, only start tracking is enabled
  /// - Last waypoint is an end point: Only start tracking is enabled
  /// - Last waypoint is a start point: Add waypoint and stop are enabled
  /// - Last waypoint is a regular point: Add waypoint and stop are enabled
  ///
  /// For active tracking (non-ended routes), it stores both the start and
  /// previous coordinates to maintain continuity in the tracking.
  Future<void> fetchLatestWaypointStatus() async {
    try {
      final dio = Dio();
      final authToken = await TokenStorage.getToken();
      final projectId = homeController.selectedProject.value?.projectId;
      final url = '$baseUrl/api/projects/$projectId/waypoints';

      if (projectId == null || authToken == null) return;

      final response = await dio.get(
        url,
        options: Options(headers: {'Authorization': 'Bearer $authToken'}),
      );

      if (response.statusCode == 200 && response.data['success'] == true) {
        final List<dynamic> waypointArrays = response.data['waypoints'];

        if (waypointArrays.isEmpty) {
          // New project, no waypoints yet
          canStart.value = true;
          canAddWaypoint.value = false;
          canStop.value = false;

          startCoordinates.value = null;
          return;
        }
        if (waypointArrays.isNotEmpty) {
          final List<dynamic> lastArray = waypointArrays.last;
          if (lastArray.isNotEmpty) {
            final Map<String, dynamic> lastWaypoint = lastArray.last;
            final bool lastIsStart = lastWaypoint['isStart'] == true;
            final bool lastIsEnd = lastWaypoint['isEnd'] == true;

            if (lastIsEnd) {
              // Show only track button
              canStart.value = true;
              canAddWaypoint.value = false;
              canStop.value = false;

              previousCoordinates.value = null;

              startCoordinates.value = null;

            } else if (lastIsStart) {
              // Show location + stop
              canStart.value = false;
              canAddWaypoint.value = true;
              canStop.value = true;

            } else if (!lastIsStart && !lastIsEnd) {
              // Show location + stop, disable track
              canStart.value = false;
              canAddWaypoint.value = true;
              canStop.value = true;
            }

            if (!lastIsEnd)
            {
              final Map<String, dynamic> firstWaypoint = lastArray.first;
              startCoordinates.value = {
                'latitude': firstWaypoint['latitude']?.toDouble() ?? 0.0,
                'longitude': firstWaypoint['longitude']?.toDouble() ?? 0.0,
              };

              final Map<String, dynamic> lastWaypoint = lastArray.last;
              previousCoordinates.value = {
                'latitude': lastWaypoint['latitude']?.toDouble() ?? 0.0,
                'longitude': lastWaypoint['longitude']?.toDouble() ?? 0.0,
              };
            }
          }
        }
      }
    } catch (e) {
      debugPrint("Error fetching waypoint status: $e");
    }
  }

  /// Displays the map screen for a specific project
  ///
  /// This method:
  /// 1. Sets the selected project for the map view
  /// 2. Updates the showMapScreen flag to true
  /// 3. Calls update() to refresh the UI
  ///
  /// @param project The Projects object containing the project details to display on the map
  void showProjectMap(Projects project) {
    selectedMapProject.value = project;
    showMapScreen.value = true;
    update();
  }

  /// Hides the map screen and returns to the previous view
  ///
  /// This method:
  /// 1. Sets the showMapScreen flag to false
  /// 2. Calls update() to refresh the UI
  ///
  /// This is typically called when the user navigates back from the map view.
  void hideMapScreen() {
    showMapScreen.value = false;
    update();
  }

  /// Changes the active tab in the bottom navigation bar
  ///
  /// This method:
  /// 1. Updates the selectedIndex to the new tab index
  /// 2. Uses the PageController to jump to the corresponding page
  /// 3. Resets the floating action button state
  /// 4. Removes any active overlay
  ///
  /// @param index The index of the tab to switch to
  void changeTab(int index) {
    selectedIndex.value = index;
    pageController.jumpToPage(index);
    floatingSelected.value = false;
    removeOverlay();
  }

  /// Toggles the visibility of additional floating action buttons
  ///
  /// This method:
  /// 1. Checks if a project is selected or map screen is active
  /// 2. Shows an error message if no project is selected
  /// 3. Toggles the showAdditionalButtons state
  /// 4. Updates the floatingSelected state to match
  /// 5. Shows or removes the overlay with additional buttons based on conditions
  ///
  /// The additional buttons are only shown when:
  /// - The home tab (index 0) is selected
  /// - Either the map screen is visible or a project is selected
  /// - The showAdditionalButtons flag is true
  void toggleAdditionalButtons() {
    if (homeController.selectedProject.value == null &&
        showMapScreen.value == false) {
      Get.snackbar(
        'No Project Selected',
        'Please select a project first',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.red,
        colorText: AppColors.white,
      );
      return;
    }

    showAdditionalButtons.toggle();
    floatingSelected.value = showAdditionalButtons.value;

    if (showAdditionalButtons.value &&
        selectedIndex.value == 0 &&
        (showMapScreen.value || homeController.selectedProject.value != null)) {
      showAdditionalButtonsOverlay();
    } else {
      removeOverlay();
    }
  }

  /// Creates and displays an overlay with additional floating action buttons
  ///
  /// This method:
  /// 1. Uses a post-frame callback to ensure the UI is fully rendered
  /// 2. Gets the overlay context and validates it
  /// 3. Calculates the position for the overlay based on the screen dimensions
  /// 4. Creates an OverlayEntry with three positioned buttons:
  ///    - Add waypoint button (top center)
  ///    - Start tracking button (bottom left)
  ///    - Stop tracking button (bottom right)
  /// 5. Inserts the overlay into the widget tree
  ///
  /// Each button's state (enabled/disabled) is determined by the current tracking state
  /// and is reactively updated using Obx.
  void showAdditionalButtonsOverlay() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final context = Get.overlayContext;
      if (context == null) return;

      final renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox == null || !renderBox.hasSize) return;

      final position = renderBox.localToGlobal(Offset.zero);

      overlayEntry = OverlayEntry(
        builder:
            (context) => Positioned(
              left: position.dx + renderBox.size.width / 2 - 100,
              bottom: MediaQuery.of(context).viewInsets.bottom + 80,
              child: Material(
                color: Colors.transparent,
                child: SizedBox(
                  width: 200,
                  height: 120,
                  child: Stack(
                    children: [
                      Positioned(
                        top: 20,
                        left: 80,
                        child: Obx(
                          () => buildAdditionalButton(
                            "assets/png/location_icon.png",
                            canAddWaypoint.value
                                ? () {
                                  isStart.value = false;
                                  isEnd.value = false;
                                  Get.to(() => WayPointScreen());
                                }
                                : null,
                          ),
                        ),
                      ),
                      Positioned(
                        top: 60,
                        left: 10,
                        child: Obx(
                          () => buildAdditionalButton(
                            "assets/png/track_icon.png",
                            canStart.value
                                ? () {
                                  isStart.value = true;
                                  isEnd.value = false;
                                  Get.to(() => WayPointScreen());
                                }
                                : null,
                          ),
                        ),
                      ),
                      Positioned(
                        top: 60,
                        right: 0,
                        child: Obx(
                          () => buildAdditionalButton(
                            "assets/png/stop_icon.png",
                            canStop.value
                                ? () {
                                  isStart.value = false;
                                  isEnd.value = true;
                                  Get.to(() => WayPointScreen());
                                }
                                : null,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
      );

      Overlay.of(context).insert(overlayEntry!);
    });
  }

  /// Removes the additional buttons overlay from the screen
  ///
  /// This method:
  /// 1. Safely removes the overlay entry if it exists
  /// 2. Sets the overlayEntry to null to free up resources
  ///
  /// This is called when the overlay should be hidden, such as when:
  /// - The user taps outside the overlay
  /// - The user changes tabs
  /// - The controller is being disposed
  void removeOverlay() {
    overlayEntry?.remove();
    overlayEntry = null;
  }

  /// Builds a floating action button with the specified icon and callback
  ///
  /// This method:
  /// 1. Creates a FloatingActionButton with appropriate styling
  /// 2. Handles the enabled/disabled state based on the provided callback
  /// 3. Sets up the button to remove the overlay when pressed
  /// 4. Displays the specified icon with appropriate opacity
  ///
  /// @param icon The asset path to the icon image to display on the button
  /// @param onPressed The callback to execute when the button is pressed, or null if the button should be disabled
  /// @return A Widget representing the styled floating action button
  Widget buildAdditionalButton(String icon, VoidCallback? onPressed) {
    final isDisabled = onPressed == null;

    return FloatingActionButton(
      backgroundColor:
          isDisabled ? Colors.grey.shade400 : AppColors.primaryOrange,
      onPressed:
          isDisabled
              ? null
              : () {
                onPressed();
                removeOverlay();
                showAdditionalButtons.value = false;
              },
      shape: const CircleBorder(),
      child: Opacity(
        opacity: isDisabled ? 0.4 : 1.0,
        child: Image.asset(icon, color: AppColors.white, width: 24, height: 24),
      ),
    );
  }

  /// Handles the general button press action for the floating action buttons
  ///
  /// This method:
  /// 1. Removes any active overlay
  /// 2. Sets the showAdditionalButtons flag to false
  ///
  /// This is a utility method that can be called when any button is pressed
  /// to ensure the overlay is properly cleaned up.
  void handleButtonPressed() {
    removeOverlay();
    showAdditionalButtons.value = false;
  }
}
