import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:dio/dio.dart';
import 'package:google_polyline_algorithm/google_polyline_algorithm.dart';
import 'package:ems/common/constants/general.dart';
import 'package:ems/common/utils/shared_preferences.dart';

/// CreateTrackController manages the track creation and visualization functionality
///
/// This controller is responsible for:
/// - Handling device location services and permissions
/// - Fetching and managing waypoint data
/// - Creating and rendering map markers and polylines
/// - Drawing direct polylines between waypoints
/// - Creating custom map markers
/// - Managing the loading state during async operations
///
/// It uses GetX for reactive state management and the Google Maps Flutter plugin
/// for map rendering and interaction.
class CreateTrackController extends GetxController {
  final Location _location = Location();
  final dio = Dio();

  static const LatLng newDelhiLocation = LatLng(28.6139, 77.2090);

  final Rxn<LatLng> currentLocation = Rxn<LatLng>();
  final RxSet<Marker> markers = <Marker>{}.obs;
  final RxSet<Polyline> polylines = <Polyline>{}.obs;
  final RxList<Map<String, dynamic>> waypoints = <Map<String, dynamic>>[].obs;
  final RxString routeName = ''.obs;
  final RxList<List<Map<String, dynamic>>> allRoutes = <List<Map<String, dynamic>>>[].obs;

  final RxBool locationServicesFailed = false.obs;
  final RxBool isLoading = true.obs;

  late GoogleMapController mapController;

  /// Initializes the controller when it's first created
  ///
  /// This method:
  /// 1. Calls the parent class's onInit method
  /// 2. Checks if arguments were passed during navigation
  /// 3. Extracts waypoints and route name from the arguments if available
  /// 4. Initializes the controller's state with the provided data
  ///
  /// The arguments are expected to be a Map containing:
  /// - 'waypoints': List of waypoint data
  /// - 'routeName': String name of the route
  @override
  void onInit() {
    super.onInit();

    if (Get.arguments != null) {
      final args = Get.arguments as Map<String, dynamic>;
      waypoints.assignAll(args['waypoints'] ?? []);
      routeName.value = args['routeName'] ?? '';
    }

    // Always fetch waypoints from API for conditional markers and polylines
    _initializeWithApiData();
  }

  /// Initializes the controller and waits for API data before rendering
  Future<void> _initializeWithApiData() async {
    isLoading.value = true;
    await fetchEmployeeWaypoints();
    isLoading.value = false;
  }

  /// Fetches employee waypoints from the API for conditional markers and polylines
  ///
  /// This method:
  /// 1. Retrieves the authentication token from storage
  /// 2. Makes an authenticated API request to get all employee waypoints
  /// 3. Processes the response to extract waypoint data with gpsDetails and poleDetails
  /// 4. Stores all routes in allRoutes for conditional marker and polyline rendering
  Future<void> fetchEmployeeWaypoints() async {
    try {
      final authToken = await TokenStorage.getToken();
      if (authToken == null) {
        return;
      }

      final url = '$baseUrl/api/projects/employee-waypoints';
      final response = await dio.get(
        url,
        options: Options(headers: {'Authorization': 'Bearer $authToken'}),
      );

      if (response.statusCode == 200 && response.data['success'] == true) {
        final List<dynamic> projects = response.data['projects'];
        allRoutes.clear();

        for (var project in projects) {
          if (project['waypoints'] != null) {
            for (var route in project['waypoints']) {
              if (route != null && route.isNotEmpty) {
                // Store each route separately for conditional rendering
                final routeWaypoints = List<Map<String, dynamic>>.from(route);
                allRoutes.add(routeWaypoints);
              }
            }
          }
        }
      }
    } catch (e) {
      // Handle error silently or use proper logging
    }
  }

  /// Fetches the current device location with proper error handling
  ///
  /// This method:
  /// 1. Sets the loading state to true
  /// 2. Checks if location services are enabled and requests them if not
  /// 3. Verifies location permissions and requests them if needed
  /// 4. Sets location accuracy to high for better precision
  /// 5. Attempts to get the current device location
  /// 6. Updates the currentLocation with the device position if successful
  /// 7. Falls back to a default location if any step fails
  /// 8. Updates the locationServicesFailed flag based on the outcome
  ///
  /// The method only updates currentLocation if waypoints are empty,
  /// as waypoints take precedence over the current device location.
  /// If any error occurs, it falls back to the default location (New Delhi).
  Future<void> fetchDeviceLocation() async {
    try {
      isLoading.value = true;

      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          locationServicesFailed.value = true;

          if (waypoints.isEmpty) {
            currentLocation.value = newDelhiLocation;
          }
          return;
        }
      }

      PermissionStatus permissionGranted = await _location.hasPermission();
      if (permissionGranted == PermissionStatus.denied) {
        permissionGranted = await _location.requestPermission();
        if (permissionGranted != PermissionStatus.granted) {
          locationServicesFailed.value = true;

          if (waypoints.isEmpty) {
            currentLocation.value = newDelhiLocation;
          }
          return;
        }
      }

      await _location.changeSettings(accuracy: LocationAccuracy.high);

      try {
        final locationData = await _location.getLocation();

        if (locationData.latitude == null || locationData.longitude == null) {
          locationServicesFailed.value = true;

          if (waypoints.isEmpty) {
            currentLocation.value = newDelhiLocation;
          }
          return;
        }

        final LatLng position = LatLng(
          locationData.latitude!,
          locationData.longitude!,
        );

        if (waypoints.isEmpty) {
          currentLocation.value = position;
        }

        locationServicesFailed.value = false;
      } catch (e) {
        locationServicesFailed.value = true;

        if (waypoints.isEmpty) {
          currentLocation.value = newDelhiLocation;
        }
      }
    } catch (e) {
      locationServicesFailed.value = true;

      if (waypoints.isEmpty) {
        currentLocation.value = newDelhiLocation;
      }
    }
  }

  /// Fetches and decodes a route between two points using Google Directions API
  ///
  /// This method:
  /// 1. Constructs a URL for the Google Directions API with start and end coordinates
  /// 2. Makes an API request to get the route information
  /// 3. Extracts the encoded polyline from the response
  /// 4. Decodes the polyline into a list of LatLng coordinates
  ///
  /// The route is optimized for driving directions by default.
  /// If the API request fails or returns an error status, an empty list is returned.
  ///
  /// @param start The starting point coordinates
  /// @param end The destination point coordinates
  /// @return A List of LatLng coordinates representing the route
  Future<List<LatLng>> getRouteCoordinates(LatLng start, LatLng end) async {
    final url =
        'https://maps.googleapis.com/maps/api/directions/json?origin=${start.latitude},${start.longitude}&destination=${end.latitude},${end.longitude}&mode=walking&key=$googleApiKey';

    final response = await dio.get(url);
    final data = response.data;

    if (data['status'] == 'OK') {
      final route = data['routes'][0];
      final overviewPolyline = route['overview_polyline']['points'];

      final decodedPoints = decodePolyline(overviewPolyline);
      return decodedPoints
          .map((p) => LatLng(p[0].toDouble(), p[1].toDouble()))
          .toList();
    } else {
      return [];
    }
  }

  /// Creates a custom circular marker icon with the specified color
  ///
  /// This method uses Flutter's Canvas API to:
  /// 1. Create a circular dot with the specified color
  /// 2. Convert the drawing to a bitmap image
  /// 3. Return a BitmapDescriptor that can be used as a marker icon on Google Maps
  ///
  /// The marker is created as a small colored circle with a transparent background,
  /// which provides a cleaner look than the default markers.
  ///
  /// @param color The color to use for the dot marker
  /// @return A BitmapDescriptor object that can be used as a marker icon
  Future<BitmapDescriptor> createDotMarker(Color color) async {
    final double size = 40.0;
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(
      pictureRecorder,
      Rect.fromPoints(Offset(0, 0), Offset(size, size)),
    );

    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final radius = size / 4;
    final center = Offset(size / 2, size / 2);
    canvas.drawCircle(center, radius, paint);

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// Creates a custom triangular marker icon with the specified color
  ///
  /// This method uses Flutter's Canvas API to:
  /// 1. Create a triangular shape with the specified color
  /// 2. Convert the drawing to a bitmap image
  /// 3. Return a BitmapDescriptor that can be used as a marker icon on Google Maps
  ///
  /// The marker is created as a small colored triangle with a transparent background,
  /// which provides a visual distinction for Double Pole Structure waypoints.
  ///
  /// @param color The color to use for the triangle marker
  /// @return A BitmapDescriptor object that can be used as a marker icon
  Future<BitmapDescriptor> createTriangleMarker(Color color) async {
    final double size = 40.0;
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(
      pictureRecorder,
      Rect.fromPoints(Offset(0, 0), Offset(size, size)),
    );

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    // Draw a triangle pointing upward
    path.moveTo(size / 2, size / 4); // Top point
    path.lineTo(size / 4, 3 * size / 4); // Bottom left
    path.lineTo(3 * size / 4, 3 * size / 4); // Bottom right
    path.close();

    canvas.drawPath(path, paint);

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// Creates a custom rectangular marker icon with the specified color
  ///
  /// This method uses Flutter's Canvas API to:
  /// 1. Create a rectangular shape with the specified color
  /// 2. Convert the drawing to a bitmap image
  /// 3. Return a BitmapDescriptor that can be used as a marker icon on Google Maps
  ///
  /// The marker is created as a small colored rectangle with a transparent background,
  /// which provides a visual distinction for waypoints with transformer information.
  ///
  /// @param color The color to use for the rectangle marker
  /// @return A BitmapDescriptor object that can be used as a marker icon
  Future<BitmapDescriptor> createRectangleMarker(Color color) async {
    final double size = 40.0;
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(
      pictureRecorder,
      Rect.fromPoints(Offset(0, 0), Offset(size, size)),
    );

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Draw a rectangle in the center of the canvas
    final rect = Rect.fromCenter(
      center: Offset(size / 2, size / 2),
      width: size / 2,
      height: size / 3,
    );
    canvas.drawRect(rect, paint);

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size.toInt(), size.toInt());
    final byteData = await img.toByteData(format: ImageByteFormat.png);
    final uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// Renders waypoints and direct polylines on the map
  ///
  /// This comprehensive method:
  /// 1. Sets the loading state to true during the rendering process
  /// 2. Handles the case when no waypoints are available by showing a fallback location
  /// 3. Extracts valid waypoint positions from the waypoints data
  /// 4. Creates custom markers for each waypoint with appropriate colors and shapes based on poleDetails and gpsDetails
  /// 5. Creates a direct polyline connecting all waypoints with style based on routeType
  /// 6. Updates the map's markers and polylines collections
  /// 7. Sets the map's initial position to the first waypoint or fallback location
  /// 8. Handles any errors that might occur during the process
  /// 9. Sets the loading state to false when complete
  ///
  /// Marker colors and shapes are determined by the following rules:
  /// - Colors:
  ///   - If poleDetails exists with 'existingOrNewProposed' = 'Existing': Black marker
  ///   - If poleDetails exists with 'existingOrNewProposed' = 'New': Orange marker (#FB8500)
  ///   - If poleDetails is empty but gpsDetails has 'transformerType' = 'Existing': Black marker
  ///   - If poleDetails is empty but gpsDetails has 'transformerType' = 'New': Orange marker (#FB8500)
  ///   - Otherwise: Red for start points, green for other points
  /// - Shapes:
  ///   - If poleDetails exists with 'poleType' = 'Double Pole Structure': Triangle marker
  ///   - If poleDetails is empty but gpsDetails has transformerType: Rectangle marker
  ///   - Otherwise: Circle marker
  ///
  /// Polyline styles are determined by the routeType:
  /// - 'New' routes: Orange solid line (#FB8500)
  /// - 'Existing' routes: Black dotted line
  Future<void> renderWaypointsOnMap() async {
    try {
      List<List<Map<String, dynamic>>> dataToRender;

      // If arguments were passed (from history screen), use only that specific route
      if (Get.arguments != null && waypoints.isNotEmpty) {
        // Use only the selected route from arguments
        dataToRender = [waypoints];
      } else {
        // If no arguments, use all routes from API
        dataToRender = allRoutes.isNotEmpty ? allRoutes : [waypoints];
      }

      if (dataToRender.isEmpty || (dataToRender.length == 1 && dataToRender.first.isEmpty)) {
        currentLocation.value = newDelhiLocation;
        final customMarker = await createDotMarker(Colors.blue);
        markers.assignAll({
          Marker(
            markerId: const MarkerId('fallback'),
            position: newDelhiLocation,
            icon: customMarker,
            infoWindow: const InfoWindow(
              title: 'No Data',
              snippet: 'No waypoints available',
            ),
          ),
        });
        polylines.clear();
        return;
      }

      final tempMarkers = <Marker>{};
      final tempPolylines = <Polyline>{};
      int polylineCounter = 0;

      // Process each route separately for proper polyline rendering
      for (int routeIndex = 0; routeIndex < dataToRender.length; routeIndex++) {
        final route = dataToRender[routeIndex];
        final routePoints = <LatLng>[];

        // For arguments data, try to find matching API data for conditional styling
        List<Map<String, dynamic>> routeWithApiData = route;
        if (Get.arguments != null && allRoutes.isNotEmpty) {
          // Try to find matching route in API data based on coordinates
          for (var apiRoute in allRoutes) {
            if (apiRoute.isNotEmpty && route.isNotEmpty) {
              final firstArgLat = route.first['latitude']?.toDouble();
              final firstArgLng = route.first['longitude']?.toDouble();
              final firstApiLat = apiRoute.first['latitude']?.toDouble();
              final firstApiLng = apiRoute.first['longitude']?.toDouble();

              if (firstArgLat != null && firstArgLng != null &&
                  firstApiLat != null && firstApiLng != null &&
                  (firstArgLat - firstApiLat).abs() < 0.0001 &&
                  (firstArgLng - firstApiLng).abs() < 0.0001) {
                // Found matching route, use API data for conditional styling
                routeWithApiData = apiRoute;
                break;
              }
            }
          }
        }

        // Determine route type and polyline color from first waypoint
        Color polylineColor = Colors.green; // Default
        List<PatternItem> patterns = [];
        String routeType = '';

        // Get route type from first waypoint if available
        if (routeWithApiData.isNotEmpty) {
          routeType = routeWithApiData[0]['routeType'] ?? '';

          if (routeType == 'New') {
            polylineColor = const Color(0xFFFB8500); // Orange
            patterns = []; // Solid line
          } else if (routeType == 'Existing') {
            polylineColor = Colors.black;
            patterns = [
              PatternItem.dash(20),
              PatternItem.gap(10),
            ];
          }
        }

        // Process each waypoint in the route
        for (int i = 0; i < route.length; i++) {
          final wp = route[i];
          final lat = wp['latitude']?.toDouble();
          final lng = wp['longitude']?.toDouble();

          if (lat == null || lng == null) continue;

          final position = LatLng(lat, lng);
          routePoints.add(position);

          // Find corresponding waypoint in API data for conditional styling
          Map<String, dynamic> wpWithApiData = wp;
          if (Get.arguments != null && routeWithApiData.length > i) {
            wpWithApiData = routeWithApiData[i];
          }

          // Determine marker color and shape based on poleDetails or gpsDetails
          Color markerColor;
          bool isDoublePoleStructure = false;
          bool hasTransformerInfo = false;
          String transformerType = '';

          // Check if poleDetails exists and is not empty
          final poleDetails = wpWithApiData['poleDetails'];
          if (poleDetails != null && poleDetails is List && poleDetails.isNotEmpty) {
            // Get the first poleDetail and check existingOrNewProposed field
            final firstPoleDetail = poleDetails.first;
            final existingOrNewProposed = firstPoleDetail['existingOrNewProposed'];
            final poleType = firstPoleDetail['poleType'];

            // Check if this is a Double Pole Structure
            isDoublePoleStructure = poleType == 'Double Pole Structure';

            if (existingOrNewProposed == 'Existing') {
              markerColor = Colors.black;
            } else if (existingOrNewProposed == 'New') {
              markerColor = const Color(0xFFFB8500); // Orange
            } else {
              // Default color logic
              markerColor = Colors.blue;
            }
          } else {
            // If poleDetails is empty, check gpsDetails for transformerType
            final gpsDetails = wpWithApiData['gpsDetails'];
            if (gpsDetails != null && gpsDetails is List && gpsDetails.isNotEmpty) {
              final firstGpsDetail = gpsDetails.first;
              transformerType = firstGpsDetail['transformerType'] ?? '';

              // Only consider transformer info if it's not empty
              if (transformerType.isNotEmpty) {
                hasTransformerInfo = true;

                if (transformerType == 'Existing') {
                  markerColor = Colors.black;
                } else if (transformerType == 'New') {
                  markerColor = const Color(0xFFFB8500); // Orange
                } else {
                  markerColor = Colors.blue;
                }
              } else {
                // Default color logic if no transformerType
                markerColor = Colors.blue;
              }
            } else {
              // Default color logic if no gpsDetails
              markerColor = Colors.blue;
            }
          }

          // Create appropriate marker based on conditions
          final BitmapDescriptor customMarker;
          if (isDoublePoleStructure) {
            // Triangle marker for Double Pole Structure
            customMarker = await createTriangleMarker(markerColor);
          } else if (poleDetails == null || (poleDetails is List && poleDetails.isEmpty)) {
            if (hasTransformerInfo) {
              // Rectangle marker for waypoints with transformer info but no pole details
              customMarker = await createRectangleMarker(markerColor);
            } else {
              // Circle marker for other waypoints
              customMarker = await createDotMarker(markerColor);
            }
          } else {
            // Circle marker for other waypoints
            customMarker = await createDotMarker(markerColor);
          }

          tempMarkers.add(
            Marker(
              markerId: MarkerId('waypoint_${routeIndex}_$i'),
              position: position,
              icon: customMarker,
              infoWindow: InfoWindow(
                title: 'Waypoint ${i + 1}',
                snippet: wp['isStart'] == true
                    ? 'Start'
                    : wp['isEnd'] == true
                    ? 'End'
                    : 'Waypoint',
              ),
            ),
          );
        }

        // Create polyline for this route
        if (routePoints.length >= 2) {
          tempPolylines.add(
            Polyline(
              polylineId: PolylineId('route_$polylineCounter'),
              points: routePoints,
              color: polylineColor,
              width: 4,
              patterns: patterns,
            ),
          );
          polylineCounter++;
        }
      }

      // Update markers and polylines
      markers.assignAll(tempMarkers);
      polylines.assignAll(tempPolylines);

      // Set initial camera position
      if (dataToRender.isNotEmpty && dataToRender.first.isNotEmpty) {
        final firstWaypoint = dataToRender.first.first;
        final lat = firstWaypoint['latitude']?.toDouble();
        final lng = firstWaypoint['longitude']?.toDouble();
        if (lat != null && lng != null) {
          currentLocation.value = LatLng(lat, lng);
        }
      }

    } catch (e) {
      // Fallback for error cases
      currentLocation.value = newDelhiLocation;
      final customMarker = await createDotMarker(Colors.blue);
      markers.assignAll({
        Marker(
          markerId: const MarkerId('error_fallback'),
          position: newDelhiLocation,
          icon: customMarker,
          infoWindow: const InfoWindow(
            title: 'Error',
            snippet: 'Failed to load waypoints',
          ),
        ),
      });
      polylines.clear();
    }
  }
}
