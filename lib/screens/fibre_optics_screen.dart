import 'package:ems/common/constants/app_colors.dart';
import 'package:ems/common/widgets/custom_app_bar.dart';
import 'package:flutter/material.dart';

class FibreOpticsScreen extends StatelessWidget {
  const FibreOpticsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: "Fibre Optics"),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "Comign Soon",
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryOrange,
              ),
            ),
            Text(
              "Fibre Optics Survey & Management Solution",
              style: TextStyle(fontSize: 10, fontWeight: FontWeight.normal),
            ),
          ],
        ),
      ),
    );
  }
}
