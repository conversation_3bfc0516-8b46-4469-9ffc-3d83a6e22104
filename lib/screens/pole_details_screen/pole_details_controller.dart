import 'package:ems/common/utils/clear_all_controllers.dart';
import 'package:ems/common/utils/clear_values_util.dart';
import 'package:ems/common/utils/upload_data_controller.dart';
import 'package:ems/common/utils/validators.dart';
import 'package:ems/screens/upload_image_screen/upload_image_screen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// PoleDetailsController manages the pole details and material quantities collection
///
/// This controller is responsible for:
/// - Managing a large number of form fields for pole details and material quantities
/// - Providing default values for different pole structure types
/// - Auto-filling material quantities based on pole structure selection
/// - Validating form data before submission
/// - Storing collected data for upload
/// - Clearing form data when needed
///
/// It uses GetX for reactive state management and provides a comprehensive
/// interface for collecting detailed information about utility poles.
class PoleDetailsController extends GetxController {
  final poleNumberController = TextEditingController();
  final existingNewProposedController = TextEditingController();
  final poleDescriptionController = TextEditingController();
  final poleTypeController = TextEditingController();
  final poleSizeInMeterController = TextEditingController();
  final poleStructureController = TextEditingController();
  final threePhaseLTDistributionBoxController = TextEditingController();
  final abSwitchController = TextEditingController();
  final anchorRodController = TextEditingController();
  final anchoringAssemblyController = TextEditingController();
  final angle4FeetController = TextEditingController();
  final angle9FeetController = TextEditingController();
  final basePlatController = TextEditingController();
  final channel4FeetController = TextEditingController();
  final channel9FeetController = TextEditingController();
  final doChannelController = TextEditingController();
  final doChannelBackClampController = TextEditingController();
  final doFuseController = TextEditingController();
  final discHardwareController = TextEditingController();
  final discInsulatorPolymericController = TextEditingController();
  final discInsulatorPorcelainController = TextEditingController();
  final dtrBaseChannelController = TextEditingController();
  final dtrSpottingAngleController = TextEditingController();
  final dvcConductorController = TextEditingController();
  final earthingConductorController = TextEditingController();
  final elbowController = TextEditingController();
  final eyeBoltController = TextEditingController();
  final giPinController = TextEditingController();
  final giPipeController = TextEditingController();
  final greeperController = TextEditingController();
  final guyInsulatorController = TextEditingController();
  final iHuckClampController = TextEditingController();
  final lightingArrestorController = TextEditingController();
  final pinInsulatorPolymericController = TextEditingController();
  final pinInsulatorPorcelainController = TextEditingController();
  final poleEarthingController = TextEditingController();
  final sideClampController = TextEditingController();
  final spottingAngleController = TextEditingController();
  final spottingChannelController = TextEditingController();
  final stayClampController = TextEditingController();
  final stayInsulatorController = TextEditingController();
  final stayRoadController = TextEditingController();
  final stayWire712Controller = TextEditingController();
  final stayWire74Controller = TextEditingController();
  final suspensionAssemblyClampController = TextEditingController();
  final topChannelController = TextEditingController();
  final topClampController = TextEditingController();
  final turnBuckleController = TextEditingController();
  final vCrossArmController = TextEditingController();
  final vCrossBackClampController = TextEditingController();
  final xBressingController = TextEditingController();
  final earthingCoilController = TextEditingController();

  final RxString threePhaseLTDistributionBox = ''.obs;
  final RxString abSwitch = ''.obs;
  final RxString anchorRod = ''.obs;
  final RxString anchoringAssembly = ''.obs;
  final RxString angle4Feet = ''.obs;
  final RxString angle9Feet = ''.obs;
  final RxString basePlat = ''.obs;
  final RxString channel4Feet = ''.obs;
  final RxString channel9Feet = ''.obs;
  final RxString doChannel = ''.obs;
  final RxString doChannelBackClamp = ''.obs;
  final RxString doFuse = ''.obs;
  final RxString discHardware = ''.obs;
  final RxString discInsulatorPolymeric = ''.obs;
  final RxString discInsulatorPorcelain = ''.obs;
  final RxString dtrBaseChannel = ''.obs;
  final RxString dtrSpottingAngle = ''.obs;
  final RxString dvcConductor = ''.obs;
  final RxString earthingConductor = ''.obs;
  final RxString elbow = ''.obs;
  final RxString eyeBolt = ''.obs;
  final RxString giPin = ''.obs;
  final RxString giPipe = ''.obs;
  final RxString greeper = ''.obs;
  final RxString guyInsulator = ''.obs;
  final RxString iHuckClamp = ''.obs;
  final RxString lightingArrestor = ''.obs;
  final RxString pinInsulatorPolymeric = ''.obs;
  final RxString pinInsulatorPorcelain = ''.obs;
  final RxString poleEarthing = ''.obs;
  final RxString sideClamp = ''.obs;
  final RxString spottingAngle = ''.obs;
  final RxString spottingChannel = ''.obs;
  final RxString stayClamp = ''.obs;
  final RxString stayInsulator = ''.obs;
  final RxString stayRoad = ''.obs;
  final RxString stayWire712 = ''.obs;
  final RxString stayWire74 = ''.obs;
  final RxString suspensionAssemblyClamp = ''.obs;
  final RxString topChannel = ''.obs;
  final RxString topClamp = ''.obs;
  final RxString turnBuckle = ''.obs;
  final RxString vCrossArm = ''.obs;
  final RxString vCrossArmBackClamp = ''.obs;
  final RxString xBressing = ''.obs;
  final RxString earthingCoil = ''.obs;

  final Map<String, RxString> fieldControllers = {};

  /// Initializes the controller when it's first created
  ///
  /// This method:
  /// 1. Calls the parent class's onInit method
  /// 2. Populates the fieldControllers map with all material quantity reactive variables
  ///
  /// The fieldControllers map is used to manage all the material quantity fields
  /// in a more maintainable way, allowing for batch operations on these fields.
  @override
  void onInit() {
    super.onInit();

    fieldControllers.addAll({
      'distributionBox': threePhaseLTDistributionBox,
      'abSwitch': abSwitch,
      'anchorRod': anchorRod,
      'anchoringAssembly': anchoringAssembly,
      'angle4Feet': angle4Feet,
      'angle9Feet': angle9Feet,
      'basePlat': basePlat,
      'channel4Feet': channel4Feet,
      'channel9Feet': channel9Feet,
      'doChannel': doChannel,
      'doChannelBackClamp': doChannelBackClamp,
      'doFuse': doFuse,
      'discHardware': discHardware,
      'discInsulatorPolymeric': discInsulatorPolymeric,
      'discInsulatorPorcelain': discInsulatorPorcelain,
      'dtrBaseChannel': dtrBaseChannel,
      'dtrSpottingAngle': dtrSpottingAngle,
      'dvcConductor': dvcConductor,
      'earthingConductor': earthingConductor,
      'elbow': elbow,
      'eyeBolt': eyeBolt,
      'giPin': giPin,
      'giPipe': giPipe,
      'greeper': greeper,
      'guyInsulator': guyInsulator,
      'iHuckClamp': iHuckClamp,
      'lightingArrestor': lightingArrestor,
      'pinInsulatorPolymeric': pinInsulatorPolymeric,
      'pinInsulatorPorcelain': pinInsulatorPorcelain,
      'poleEarthing': poleEarthing,
      'sideClamp': sideClamp,
      'spottingAngle': spottingAngle,
      'spottingChannel': spottingChannel,
      'stayClamp': stayClamp,
      'stayInsulator': stayInsulator,
      'stayRoad': stayRoad,
      'stayWire712': stayWire712,
      'stayWire74': stayWire74,
      'suspensionAssemblyClamp': suspensionAssemblyClamp,
      'topChannel': topChannel,
      'topClamp': topClamp,
      'turnBuckle': turnBuckle,
      'vCrossArm': vCrossArm,
      'vCrossArmBackClamp': vCrossArmBackClamp,
      'xBressing': xBressing,
      'earthingCoil': earthingCoil,
    });
  }

  // Defalut values of pole structure
  final Map<String, Map<String, String>> poleStructures = {
    'PCC Pole/ PSC Pole': {
      'distributionBox': '0',
      'abSwitch': '0',
      'anchorRod': '1',
      'anchoringAssembly': '1',
      'angle4Feet': '1',
      'angle9Feet': '0',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '0',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '0',
      'discHardware': '0',
      'discInsulatorPolymeric': '0',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '0',
      'dtrSpottingAngle': '0',
      'dvcConductor': '0',
      'earthingConductor': '1',
      'elbow': '0',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '0',
      'guyInsulator': '1',
      'iHuckClamp': '0',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '0',
      'poleEarthing': '1',
      'sideClamp': '2',
      'spottingAngle': '0',
      'spottingChannel': '0',
      'stayClamp': '1',
      'stayInsulator': '1',
      'stayRoad': '1',
      'stayWire712': '5.5',
      'stayWire74': '6',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '1',
      'vCrossArm': '1',
      'vCrossArmBackClamp': '1',
      'xBressing': '0',
      'earthingCoil': '1',
    },
    'RSJ Pole': {
      'distributionBox': '0',
      'abSwitch': '0',
      'anchorRod': '1',
      'anchoringAssembly': '1',
      'angle4Feet': '1',
      'angle9Feet': '0',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '0',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '0',
      'discHardware': '0',
      'discInsulatorPolymeric': '0',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '0',
      'dtrSpottingAngle': '0',
      'dvcConductor': '0',
      'earthingConductor': '1',
      'elbow': '0',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '0',
      'guyInsulator': '1',
      'iHuckClamp': '0',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '0',
      'poleEarthing': '1',
      'sideClamp': '2',
      'spottingAngle': '0',
      'spottingChannel': '0',
      'stayClamp': '1',
      'stayInsulator': '1',
      'stayRoad': '1',
      'stayWire712': '6.5',
      'stayWire74': '7.5',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '1',
      'vCrossArm': '1',
      'vCrossArmBackClamp': '1',
      'xBressing': '0',
      'earthingCoil': '1',
    },
    'Rail Pole': {
      'distributionBox': '0',
      'abSwitch': '0',
      'anchorRod': '1',
      'anchoringAssembly': '1',
      'angle4Feet': '1',
      'angle9Feet': '0',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '0',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '0',
      'discHardware': '0',
      'discInsulatorPolymeric': '0',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '0',
      'dtrSpottingAngle': '0',
      'dvcConductor': '0',
      'earthingConductor': '1',
      'elbow': '0',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '0',
      'guyInsulator': '1',
      'iHuckClamp': '0',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '0',
      'poleEarthing': '1',
      'sideClamp': '2',
      'spottingAngle': '0',
      'spottingChannel': '0',
      'stayClamp': '1',
      'stayInsulator': '1',
      'stayRoad': '1',
      'stayWire712': '6.5',
      'stayWire74': '7',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '1',
      'vCrossArm': '1',
      'vCrossArmBackClamp': '1',
      'xBressing': '0',
      'earthingCoil': '1',
    },
    'H-Beam Pole': {
      'distributionBox': '0',
      'abSwitch': '0',
      'anchorRod': '1',
      'anchoringAssembly': '1',
      'angle4Feet': '1',
      'angle9Feet': '0',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '0',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '0',
      'discHardware': '0',
      'discInsulatorPolymeric': '0',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '0',
      'dtrSpottingAngle': '0',
      'dvcConductor': '0',
      'earthingConductor': '1',
      'elbow': '0',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '0',
      'guyInsulator': '1',
      'iHuckClamp': '0',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '0',
      'poleEarthing': '1',
      'sideClamp': '2',
      'spottingAngle': '0',
      'spottingChannel': '0',
      'stayClamp': '1',
      'stayInsulator': '1',
      'stayRoad': '1',
      'stayWire712': '6.5',
      'stayWire74': '7.5',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '1',
      'vCrossArm': '1',
      'vCrossArmBackClamp': '1',
      'xBressing': '0',
      'earthingCoil': '1',
    },
    'Tubular Pole': {
      'distributionBox': '0',
      'abSwitch': '0',
      'anchorRod': '1',
      'anchoringAssembly': '1',
      'angle4Feet': '1',
      'angle9Feet': '0',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '0',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '0',
      'discHardware': '0',
      'discInsulatorPolymeric': '0',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '0',
      'dtrSpottingAngle': '0',
      'dvcConductor': '0',
      'earthingConductor': '1',
      'elbow': '0',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '0',
      'guyInsulator': '1',
      'iHuckClamp': '0',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '0',
      'poleEarthing': '1',
      'sideClamp': '2',
      'spottingAngle': '0',
      'spottingChannel': '0',
      'stayClamp': '1',
      'stayInsulator': '1',
      'stayRoad': '1',
      'stayWire712': '6',
      'stayWire74': '7',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '1',
      'vCrossArm': '1',
      'vCrossArmBackClamp': '1',
      'xBressing': '0',
      'earthingCoil': '1',
    },
    'Steel Tubular Pole': {
      'distributionBox': '0',
      'abSwitch': '0',
      'anchorRod': '1',
      'anchoringAssembly': '1',
      'angle4Feet': '1',
      'angle9Feet': '0',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '0',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '0',
      'discHardware': '0',
      'discInsulatorPolymeric': '0',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '0',
      'dtrSpottingAngle': '0',
      'dvcConductor': '0',
      'earthingConductor': '1',
      'elbow': '0',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '0',
      'guyInsulator': '1',
      'iHuckClamp': '0',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '0',
      'poleEarthing': '1',
      'sideClamp': '2',
      'spottingAngle': '0',
      'spottingChannel': '0',
      'stayClamp': '1',
      'stayInsulator': '1',
      'stayRoad': '1',
      'stayWire712': '5.5',
      'stayWire74': '6',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '1',
      'vCrossArm': '1',
      'vCrossArmBackClamp': '1',
      'xBressing': '0',
      'earthingCoil': '1',
    },
    'Double Pole Structure': {
      'distributionBox': '0',
      'abSwitch': '0',
      'anchorRod': '1',
      'anchoringAssembly': '1',
      'angle4Feet': '1',
      'angle9Feet': '0',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '0',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '0',
      'discHardware': '0',
      'discInsulatorPolymeric': '0',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '0',
      'dtrSpottingAngle': '0',
      'dvcConductor': '0',
      'earthingConductor': '1',
      'elbow': '0',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '0',
      'guyInsulator': '1',
      'iHuckClamp': '0',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '0',
      'poleEarthing': '1',
      'sideClamp': '2',
      'spottingAngle': '0',
      'spottingChannel': '0',
      'stayClamp': '1',
      'stayInsulator': '1',
      'stayRoad': '1',
      'stayWire712': '5.5',
      'stayWire74': '6',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '1',
      'vCrossArm': '1',
      'vCrossArmBackClamp': '1',
      'xBressing': '0',
      'earthingCoil': '1',
    },
    'Other': {
      'distributionBox': '0',
      'abSwitch': '0',
      'anchorRod': '1',
      'anchoringAssembly': '1',
      'angle4Feet': '1',
      'angle9Feet': '0',
      'basePlat': '0',
      'channel4Feet': '0',
      'channel9Feet': '0',
      'doChannel': '0',
      'doChannelBackClamp': '0',
      'doFuse': '0',
      'discHardware': '0',
      'discInsulatorPolymeric': '0',
      'discInsulatorPorcelain': '0',
      'dtrBaseChannel': '0',
      'dtrSpottingAngle': '0',
      'dvcConductor': '0',
      'earthingConductor': '1',
      'elbow': '0',
      'eyeBolt': '0',
      'giPin': '1',
      'giPipe': '1',
      'greeper': '0',
      'guyInsulator': '1',
      'iHuckClamp': '0',
      'lightingArrestor': '0',
      'pinInsulatorPolymeric': '3',
      'pinInsulatorPorcelain': '0',
      'poleEarthing': '1',
      'sideClamp': '2',
      'spottingAngle': '0',
      'spottingChannel': '0',
      'stayClamp': '1',
      'stayInsulator': '1',
      'stayRoad': '1',
      'stayWire712': '5.5',
      'stayWire74': '6',
      'suspensionAssemblyClamp': '0',
      'topChannel': '0',
      'topClamp': '0',
      'turnBuckle': '1',
      'vCrossArm': '1',
      'vCrossArmBackClamp': '1',
      'xBressing': '0',
      'earthingCoil': '1',
    },
  };

  /// Updates material quantity fields based on the selected pole structure
  ///
  /// This method:
  /// 1. Retrieves the default values for the selected pole structure from the poleStructures map
  /// 2. If no defaults are found, returns without making changes
  /// 3. Updates all material quantity fields with the default values for the selected structure
  ///
  /// This provides a convenient way to pre-fill the form with appropriate
  /// default values based on the pole structure selection, improving
  /// data entry efficiency and accuracy.
  ///
  /// @param pole The selected pole structure type (e.g., 'PCC Pole/ PSC Pole', 'RSJ Pole', etc.)
  void updateFields(String pole) {
    final poleStructure = poleStructures[pole];
    if (poleStructure == null) return;

    for (var entry in poleStructure.entries) {
      fieldControllers[entry.key]?.value = entry.value;
    }
  }

  /// Clears all form fields and resets the form to its initial state
  ///
  /// This method:
  /// 1. Clears all TextEditingController instances using the clearControllers utility
  /// 2. Clears all reactive string variables using the ClearRxStringValues utility
  ///
  /// This comprehensive clearing ensures that all form data is reset
  /// when needed, such as after successful submission or when the user
  /// explicitly requests to clear the form.
  void clearAll() {
    clearControllers([
      poleNumberController,
      existingNewProposedController,
      poleDescriptionController,
      poleTypeController,
      poleSizeInMeterController,
      poleStructureController,
      threePhaseLTDistributionBoxController,
      abSwitchController,
      anchorRodController,
      anchoringAssemblyController,
      angle4FeetController,
      angle9FeetController,
      basePlatController,
      channel4FeetController,
      channel9FeetController,
      doChannelController,
      doChannelBackClampController,
      doFuseController,
      discHardwareController,
      discInsulatorPolymericController,
      discInsulatorPorcelainController,
      dtrBaseChannelController,
      dtrSpottingAngleController,
      dvcConductorController,
      earthingConductorController,
      elbowController,
      eyeBoltController,
      giPinController,
      giPipeController,
      greeperController,
      guyInsulatorController,
      iHuckClampController,
      lightingArrestorController,
      pinInsulatorPolymericController,
      pinInsulatorPorcelainController,
      poleEarthingController,
      sideClampController,
      spottingAngleController,
      spottingChannelController,
      stayClampController,
      stayInsulatorController,
      stayRoadController,
      stayWire712Controller,
      stayWire74Controller,
      suspensionAssemblyClampController,
      topChannelController,
      topClampController,
      turnBuckleController,
      vCrossArmController,
      vCrossBackClampController,
      xBressingController,
      earthingCoilController,
    ]);

    ClearRxStringValues.clearRxStrings([
      threePhaseLTDistributionBox,
      abSwitch,
      anchorRod,
      anchoringAssembly,
      angle4Feet,
      angle9Feet,
      basePlat,
      channel4Feet,
      channel9Feet,
      doChannel,
      doChannelBackClamp,
      doFuse,
      discHardware,
      discInsulatorPolymeric,
      discInsulatorPorcelain,
      dtrBaseChannel,
      dtrSpottingAngle,
      dvcConductor,
      earthingConductor,
      elbow,
      eyeBolt,
      giPin,
      giPipe,
      greeper,
      guyInsulator,
      iHuckClamp,
      lightingArrestor,
      pinInsulatorPolymeric,
      pinInsulatorPorcelain,
      poleEarthing,
      sideClamp,
      spottingAngle,
      spottingChannel,
      stayClamp,
      stayInsulator,
      stayRoad,
      stayWire712,
      stayWire74,
      suspensionAssemblyClamp,
      topChannel,
      topClamp,
      turnBuckle,
      vCrossArm,
      vCrossArmBackClamp,
      xBressing,
      earthingCoil,
    ]);
  }

  /// Validates all form fields and proceeds to the next screen if valid
  ///
  /// This method:
  /// 1. Creates a list to store validation errors
  /// 2. Gets a reference to the UploadDataController for data transfer
  /// 3. Validates all text input fields using the Validators utility
  /// 4. Validates all material quantity fields
  /// 5. If any errors are found, displays the first error in a snackbar
  /// 6. If all validations pass:
  ///    a. Transfers all form data to the UploadDataController
  ///    b. Clears the form
  ///    c. Navigates to the UploadImageScreen
  ///
  /// This ensures that only valid and complete data is submitted,
  /// improving data quality and preventing incomplete submissions.
  void validateAndProceed() {
    final List<String> errors = [];
    final uploadData = Get.find<UploadDataController>();

    final Map<String, TextEditingController> textControllers = {
      'Pole No.': poleNumberController,
      'Existing/New Proposed': existingNewProposedController,
      'Pole Description': poleDescriptionController,
      'Pole Type': poleTypeController,
      'Pole Size (in meter)': poleSizeInMeterController,
      'Pole Structure': poleStructureController,
    };

    textControllers.forEach((label, controller) {
      final result = Validators.validator(controller.text, label);
      if (result != null) errors.add(result);
    });

    fieldControllers.forEach((label, rxValue) {
      final result = Validators.validator(rxValue.value, label);
      if (result != null) errors.add(result);
    });

    if (errors.isNotEmpty) {
      Get.snackbar(
        'Empty Fields',
        errors.first,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } else {
      uploadData
        ..poleNumber.value = poleNumberController.text
        ..existingOrNew.value = existingNewProposedController.text
        ..poleDescription.value = poleDescriptionController.text
        ..poleType.value = poleTypeController.text
        ..poleSize.value = poleSizeInMeterController.text
        ..poleStructure.value = poleStructureController.text;

      uploadData.poleMaterialQuantities.clear();

      fieldControllers.forEach((key, rxValue) {
        uploadData.poleMaterialQuantities[key] = rxValue.value;
      });

      clearAll();

      Get.to(() => UploadImageScreen());
    }
  }
}
