import 'package:ems/common/constants/size.dart';
import 'package:ems/common/widgets/custom_app_bar.dart';
import 'package:ems/common/widgets/custom_button.dart';
import 'package:ems/common/widgets/custom_dropdown_row.dart';
import 'package:ems/common/widgets/custom_header.dart';
import 'package:ems/screens/pole_details_screen/pole_details_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PoleDetailsScreen extends StatefulWidget {
  const PoleDetailsScreen({super.key});

  @override
  State<PoleDetailsScreen> createState() => _PoleDetailsScreenState();
}

class _PoleDetailsScreenState extends State<PoleDetailsScreen> {
  PoleDetailsController controller = Get.put(PoleDetailsController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          children: [
            CustomAppBar(
              showBackButton: true,
              onPressed: Get.back,
              backgroundColor: Colors.black,
              backButtonColor: Colors.white,
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(
                  vertical: 10,
                  horizontal: 20,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CustomHeader(
                          title: 'GPS Details',
                          isEnabled: false,
                          textColor: Colors.black,
                        ),
                        1.5.pw,
                        CustomHeader(
                          title: 'Pole Details',
                          isEnabled: true,
                        ),
                      ],
                    ),
                    2.5.ph,
                    CustomDropdownRow(
                      title: 'Pole No.',
                      items: [],
                      value: controller.poleNumberController.text,
                      onChanged: (val) => controller.poleNumberController.text = val ?? '',
                      isEditable: true,
                      showDropdown: false,
                    ),
                    CustomDropdownRow(
                      title: 'Existing/New Proposed',
                      items: ['Existing', 'New'],
                      value: controller.existingNewProposedController.text,
                      onChanged: (val) => controller.existingNewProposedController.text = val ?? '',
                      isEditable: false,
                      showDropdown: true,
                    ),
                    CustomDropdownRow(
                      title: 'Pole Description',
                      items: [],
                      value: controller.poleDescriptionController.text,
                      onChanged: (val) => controller.poleDescriptionController.text = val ?? '',
                      isEditable: true,
                      showDropdown: false,
                    ),
                    CustomDropdownRow(
                      title: 'Pole Type',
                      items: ['Line Pole', 'Angle Pole', 'Tapping Pole', 'Double Pole Structure', 'Other'],
                      value: controller.poleTypeController.text,
                      onChanged: (val) => controller.poleTypeController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    ),
                    CustomDropdownRow(
                      title: 'Pole Size (in meter)',
                      items: ['8 meter', '9 meter', '11 meter', '13 meter', 'Other'],
                      value: controller.poleSizeInMeterController.text,
                      onChanged: (val) => controller.poleSizeInMeterController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    ),
                    CustomDropdownRow(
                      title: 'Pole Structure',
                      items: ['PCC Pole/ PSC Pole', 'RSJ Pole', 'Rail Pole', 'H-Beam Pole', 'Tubular Pole', 'Steel Tubular Pole', 'Double Pole Structure', 'Other'],
                      value: controller.poleStructureController.text,
                      onChanged: (val) {
                        controller.poleStructureController.text = val ?? '';
                        controller.updateFields(val ?? '');
                      },
                      isEditable: true,
                      showDropdown: true,
                    ),
                    Obx(() => CustomDropdownRow(
                      title: '3Phase L.T. Distribution box',
                      items: [],
                      value: controller.threePhaseLTDistributionBox.value,
                      onChanged: (val) => controller.threePhaseLTDistributionBoxController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'AB Switch',
                      items: [],
                      value: controller.abSwitch.value,
                      onChanged: (val) => controller.abSwitchController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Anchor Rod',
                      items: [],
                      value: controller.anchorRod.value,
                      onChanged: (val) => controller.anchorRodController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Anchoring Assembly',
                      items: [],
                      value: controller.anchoringAssembly.value,
                      onChanged: (val) => controller.anchoringAssemblyController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Angle (4 feet)',
                      items: [],
                      value: controller.angle4Feet.value,
                      onChanged: (val) => controller.angle4FeetController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Angle (9 feet)',
                      items: [],
                      value: controller.angle9Feet.value,
                      onChanged: (val) => controller.angle9FeetController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Base Plat',
                      items: [],
                      value: controller.basePlat.value,
                      onChanged: (val) => controller.basePlatController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Channel (4 feet)',
                      items: [],
                      value: controller.channel4Feet.value,
                      onChanged: (val) => controller.channel4FeetController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Channel (9 feet)',
                      items: [],
                      value: controller.channel9Feet.value,
                      onChanged: (val) => controller.channel9FeetController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'D.O. Channel',
                      items: [],
                      value: controller.doChannel.value,
                      onChanged: (val) => controller.doChannelController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'D.O. Channel Back Clamp',
                      items: [],
                      value: controller.doChannelBackClamp.value,
                      onChanged: (val) => controller.doChannelBackClampController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'D.O. Fuse',
                      items: [],
                      value: controller.doFuse.value,
                      onChanged: (val) => controller.doFuseController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Disc Hardware',
                      items: [],
                      value: controller.discHardware.value,
                      onChanged: (val) => controller.discHardwareController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Disc Insulator (Polymeric)',
                      items: [],
                      value: controller.discInsulatorPolymeric.value,
                      onChanged: (val) => controller.discInsulatorPolymericController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Disc Insulator (Porcelain)',
                      items: [],
                      value: controller.discInsulatorPorcelain.value,
                      onChanged: (val) => controller.discInsulatorPorcelainController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'DTR Base Channel',
                      items: [],
                      value: controller.dtrBaseChannel.value,
                      onChanged: (val) => controller.dtrBaseChannelController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'DTR Spotting Angle',
                      items: [],
                      value: controller.dtrSpottingAngle.value,
                      onChanged: (val) => controller.dtrSpottingAngleController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'DVC Conductor',
                      items: [],
                      value: controller.dvcConductor.value,
                      onChanged: (val) => controller.dvcConductorController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Earthing Conductor',
                      items: [],
                      value: controller.earthingConductor.value,
                      onChanged: (val) => controller.earthingConductorController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Elbow',
                      items: [],
                      value: controller.elbow.value,
                      onChanged: (val) => controller.elbowController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Eye-Bolt',
                      items: [],
                      value: controller.eyeBolt.value,
                      onChanged: (val) => controller.eyeBoltController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'GI Pin',
                      items: [],
                      value: controller.giPin.value,
                      onChanged: (val) => controller.giPinController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'GI Pipe',
                      items: [],
                      value: controller.giPipe.value,
                      onChanged: (val) => controller.giPipeController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Greeper',
                      items: [],
                      value: controller.greeper.value,
                      onChanged: (val) => controller.greeperController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Guy Insulator',
                      items: [],
                      value: controller.guyInsulator.value,
                      onChanged: (val) => controller.guyInsulatorController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'I-Huck Clamp',
                      items: [],
                      value: controller.iHuckClamp.value,
                      onChanged: (val) => controller.iHuckClampController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Lighting Arrestor',
                      items: [],
                      value: controller.lightingArrestor.value,
                      onChanged: (val) => controller.lightingArrestorController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Pin Insulator (Polymeric)',
                      items: [],
                      value: controller.pinInsulatorPolymeric.value,
                      onChanged: (val) => controller.pinInsulatorPolymericController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Pin Insulator (Porcelain)',
                      items: [],
                      value: controller.pinInsulatorPorcelain.value,
                      onChanged: (val) => controller.pinInsulatorPorcelainController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Pole Earthing',
                      items: [],
                      value: controller.poleEarthing.value,
                      onChanged: (val) => controller.poleEarthingController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Side Clamp',
                      items: [],
                      value: controller.sideClamp.value,
                      onChanged: (val) => controller.sideClampController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Spotting Angle',
                      items: [],
                      value: controller.spottingAngle.value,
                      onChanged: (val) => controller.spottingAngleController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Spotting Channel',
                      items: [],
                      value: controller.spottingChannel.value,
                      onChanged: (val) => controller.spottingChannelController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Stay Clamp',
                      items: [],
                      value: controller.stayClamp.value,
                      onChanged: (val) => controller.stayClampController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Stay Insulator',
                      items: [],
                      value: controller.stayInsulator.value,
                      onChanged: (val) => controller.stayInsulatorController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Stay Road',
                      items: [],
                      value: controller.stayRoad.value,
                      onChanged: (val) => controller.stayRoadController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Stay Wire 7/12',
                      items: [],
                      value: controller.stayWire712.value,
                      onChanged: (val) => controller.stayWire712Controller.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Stay Wire 7/4',
                      items: [],
                      value: controller.stayWire74.value,
                      onChanged: (val) => controller.stayWire74Controller.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Suspension Assembly Clamp',
                      items: [],
                      value: controller.suspensionAssemblyClamp.value,
                      onChanged: (val) => controller.suspensionAssemblyClampController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Top Channel',
                      items: [],
                      value: controller.topChannel.value,
                      onChanged: (val) => controller.topChannelController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Top Clamp',
                      items: [],
                      value: controller.topClamp.value,
                      onChanged: (val) => controller.topClampController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Turn Buckle',
                      items: [],
                      value: controller.turnBuckle.value,
                      onChanged: (val) => controller.turnBuckleController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'V Cross Arm',
                      items: [],
                      value: controller.vCrossArm.value,
                      onChanged: (val) => controller.vCrossArmController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'V Cross Back Clamp',
                      items: [],
                      value: controller.vCrossArmBackClamp.value,
                      onChanged: (val) => controller.vCrossBackClampController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'X-Bressing',
                      items: [],
                      value: controller.xBressing.value,
                      onChanged: (val) => controller.xBressingController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    Obx(() => CustomDropdownRow(
                      title: 'Earthing Coil',
                      items: [],
                      value: controller.earthingCoil.value,
                      onChanged: (val) => controller.earthingCoilController.text = val ?? '',
                      isEditable: true,
                      showDropdown: true,
                    )),
                    2.5.ph,
                    Center(
                      child: SizedBox(
                        width: 150,
                        child: CustomButton(
                          onTap: controller.validateAndProceed,
                          text: 'Next',
                        ),
                      ),
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}