import 'package:ems/models/coordinate_model.dart';

class GpsDetails {
  final String timeAndDate;
  final String userId;
  final String district;
  final String routeStartPoint;
  final double lengthInMeter;
  final Coordinate startPointCoordinates;
  final Coordinate currentWaypointCoordinates;
  final String substationName;
  final String feederName;
  final String conductor;
  final String cable;
  final String transformerLocation;
  final String transformerType;
  final String transformerPole;
  final String transformerKV;
  final Map<String, int> gpsMaterials;

  GpsDetails({
    required this.timeAndDate,
    required this.userId,
    required this.district,
    required this.routeStartPoint,
    required this.lengthInMeter,
    required this.startPointCoordinates,
    required this.currentWaypointCoordinates,
    required this.substationName,
    required this.feederName,
    required this.conductor,
    required this.cable,
    required this.transformerLocation,
    required this.transformerType,
    required this.transformerPole,
    required this.transformerKV,
    required this.gpsMaterials,
  });

  Map<String, dynamic> toJson() => {
    "timeAndDate": timeAndDate,
    "userId": userId,
    "district": district,
    "routeStartPoint": routeStartPoint,
    "lengthInMeter": lengthInMeter,
    "startPointCoordinates": startPointCoordinates.toJson(),
    "currentWaypointCoordinates": currentWaypointCoordinates.toJson(),
    "substationName": substationName,
    "feederName": feederName,
    "conductor": conductor,
    "cable": cable,
    "transformerLocation": transformerLocation,
    "transformerType": transformerType,
    "transformerPole": transformerPole,
    "transformerKV": transformerKV,
    ...gpsMaterials,
  };

  factory GpsDetails.fromJson(Map<String, dynamic> json) => GpsDetails(
    timeAndDate: json["timeAndDate"] ?? '',
    userId: json["userId"] ?? '',
    district: json["district"] ?? '',
    routeStartPoint: json["routeStartPoint"] ?? '',
    lengthInMeter: (json["lengthInMeter"] ?? 0).toDouble(),
    startPointCoordinates: Coordinate.fromJson(json["startPointCoordinates"]),
    currentWaypointCoordinates: Coordinate.fromJson(json["currentWaypointCoordinates"]),
    substationName: json["substationName"] ?? '',
    feederName: json["feederName"] ?? '',
    conductor: json["conductor"] ?? '',
    cable: json["cable"] ?? '',
    transformerLocation: json["transformerLocation"] ?? '',
    transformerType: json["transformerType"] ?? '',
    transformerPole: json["transformerPole"] ?? '',
    transformerKV: json["transformerKV"] ?? '',
    gpsMaterials: Map<String, int>.from(json)..removeWhere((k, v) => [
      "timeAndDate", "userId", "district", "routeStartPoint",
      "lengthperKm", "startPointCoordinates", "currentWaypointCoordinates",
      "substationName", "feederName", "conductor", "cable",
      "transformerLocation", "transformerType", "transformerPole", "transformerKV"
    ].contains(k))
  );
}
