class ProjectModel {
  String? message;
  int? count;
  List<Projects>? projects;

  ProjectModel({this.message, this.count, this.projects});

  ProjectModel.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    count = json['count'];
    if (json['projects'] != null) {
      projects = <Projects>[];
      json['projects'].forEach((v) {
        projects!.add(Projects.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['message'] = message;
    data['count'] = count;
    if (projects != null) {
      data['projects'] = projects!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Projects {
  String? projectId;
  String? description;
  String? circle;
  String? division;
  CreatedBy? createdBy;

  Projects({
    this.projectId,
    this.description,
    this.circle,
    this.division,
    this.createdBy,
  });

  Projects.fromJson(Map<String, dynamic> json) {
    projectId = json['projectId'];
    description = json['description'];
    circle = json['circle'];
    division = json['division'];
    createdBy =
        json['createdBy'] != null
            ? CreatedBy.fromJson(json['createdBy'])
            : null;
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['projectId'] = projectId;
    data['description'] = description;
    data['circle'] = circle;
    data['division'] = division;
    if (createdBy != null) {
      data['createdBy'] = createdBy!.toJson();
    }
    return data;
  }
}

class CreatedBy {
  String? name;
  String? email;

  CreatedBy({this.name, this.email});

  CreatedBy.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    email = json['email'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['email'] = email;
    return data;
  }
}
