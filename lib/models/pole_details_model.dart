class PoleDetails {
  final int poleNo;
  final String existingOrNewProposed;
  final String poleDiscription;
  final String poleType;
  final int poleSizeInMeter;
  final String poleStructure;
  final Map<String, int> poleMaterials;

  PoleDetails({
    required this.poleNo,
    required this.existingOrNewProposed,
    required this.poleDiscription,
    required this.poleType,
    required this.poleSizeInMeter,
    required this.poleStructure,
    required this.poleMaterials,
  });

  Map<String, dynamic> toJson() => {
    "poleNo": poleNo,
    "existingOrNewProposed": existingOrNewProposed,
    "poleDiscription": poleDiscription,
    "poleType": poleType,
    "poleSizeInMeter": poleSizeInMeter,
    "poleStructure": poleStructure,
    ...poleMaterials,
  };

  factory PoleDetails.fromJson(Map<String, dynamic> json) => PoleDetails(
    poleNo: json["poleNo"] ?? 0,
    existingOrNewProposed: json["existingOrNewProposed"] ?? '',
    poleDiscription: json["poleDiscription"] ?? '',
    poleType: json["poleType"] ?? '',
    poleSizeInMeter: json["poleSizeInMeter"] ?? 0,
    poleStructure: json["poleStructure"] ?? '',
    poleMaterials: Map<String, int>.from(json)..removeWhere((k, v) => [
      "poleNo", "existingOrNewProposed", "poleDiscription", "poleType", "poleSizeInMeter", "poleStructure"
    ].contains(k))
  );
}
