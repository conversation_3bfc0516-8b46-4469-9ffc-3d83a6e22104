class ProfileModel {
  String? name;
  String? empId;
  String? email;
  String? mobileNo;
  String? image;
  String? role;

  ProfileModel({
    this.name,
    this.empId,
    this.email,
    this.mobileNo,
    this.image,
    this.role,
  });

  ProfileModel.fromJson(Map<String, dynamic> json) {
   name = json['name']?.toString();
    empId = json['empId']?.toString();
    email = json['email']?.toString();
    mobileNo = json['mobileNo']?.toString();
    image = json['image']?.toString();
    role = json['role']?.toString();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};

    data['name'] = name;
    data['empId'] = empId;
    data['email'] = email;
    data['mobileNo'] = mobileNo;
    data['image'] = image;
    data['role'] = role;

    return data;
  }
}
