import 'package:ems/models/gps_details_model.dart';
import 'package:ems/models/pole_details_model.dart';

class Waypoint {
  final String name;
  final String description;
  final double distanceFromPrevious;
  final double latitude;
  final double longitude;
  final String routeType;
  final String routeStartingPoint;
  final String routeEndingPoint;
  final bool isStart;
  final bool isEnd;
  final String image;
  final List<GpsDetails> gpsDetails;
  final List<PoleDetails> poleDetails;

  Waypoint({
    required this.name,
    required this.description,
    required this.distanceFromPrevious,
    required this.latitude,
    required this.longitude,
    required this.routeType,
    required this.routeStartingPoint,
    required this.routeEndingPoint,
    required this.isStart,
    required this.isEnd,
    required this.image,
    required this.gpsDetails,
    required this.poleDetails,
  });

  Map<String, dynamic> toJson() => {
    "name": name,
    "description": description,
    "distanceFromPrevious": distanceFromPrevious,
    "latitude": latitude,
    "longitude": longitude,
    "routeType": routeType,
    "routeStartingPoint": routeStartingPoint,
    "routeEndingPoint": routeEndingPoint,
    "isStart": isStart,
    "isEnd": isEnd,
    "image": image,
    "gpsDetails": gpsDetails.map((e) => e.toJson()).toList(),
    "poleDetails": poleDetails.map((e) => e.toJson()).toList(),
  };

  factory Waypoint.fromJson(Map<String, dynamic> json) => Waypoint(
    name: json["name"] ?? '',
    description: json["description"] ?? '',
    distanceFromPrevious: (json["distanceFromPrevious"] ?? 0).toDouble(),
    latitude: (json["latitude"] ?? 0).toDouble(),
    longitude: (json["longitude"] ?? 0).toDouble(),
    routeType: json["routeType"] ?? '',
    routeStartingPoint: json["routeStartingPoint"] ?? '',
    routeEndingPoint: json["routeEndingPoint"] ?? '',
    isStart: json["isStart"] ?? false,
    isEnd: json["isEnd"] ?? false,
    image: json["image"] ?? '',
    gpsDetails: (json["gpsDetails"] as List<dynamic>?)
        ?.map((e) => GpsDetails.fromJson(e))
        .toList() ?? [],
    poleDetails: (json["poleDetails"] as List<dynamic>?)
        ?.map((e) => PoleDetails.fromJson(e))
        .toList() ?? [],
  );
}
